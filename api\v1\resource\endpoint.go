package resource

import "github.com/gogf/gf/v2/frame/g"

type ListResourceEndpointReq struct {
	g.Meta    `path:"/resource/endpoint/list" tags:"endpoint" method:"get" summary:"list endpoint"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Search    string `json:"search"`
	Page      int    `json:"page" d:"1"`
	Size      int    `json:"size" d:"10"`
}

type DeleteResourceEndpointReq struct {
	g.Meta    `path:"/resource/endpoint/delete" tags:"endpoint" method:"delete" summary:"delete endpoint" auth:"k8s-namespaceEndpoint-w"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace"`
	Name      string `json:"name" v:"required#Require name."`
}

type CreateResourceEndpointYamlReq struct {
	g.Meta    `path:"/resource/endpoint/yaml/create" tags:"endpoint" method:"post" summary:"create endpoint object with yaml" auth:"k8s-namespaceEndpoint-w"`
	ClusterId int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                 `json:"namespace"`
	Data      map[string]interface{} `json:"data" v:"required#Require name."`
}

type UpdateResourceEndpointYamlReq struct {
	g.Meta    `path:"/resource/endpoint/yaml/update" tags:"endpoint" method:"post" summary:"update endpoint object with yaml" auth:"k8s-namespaceEndpoint-w"`
	ClusterId int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                 `json:"namespace"`
	Data      map[string]interface{} `json:"data" v:"required#Require name."`
}
