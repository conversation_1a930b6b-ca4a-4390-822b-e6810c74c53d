package backupmananger

import "github.com/gogf/gf/v2/frame/g"

// ListBackupResourceObjectReq 查询备份资源对象
type ListBackupResourceObjectReq struct {
	g.Meta    `path:"/backup-manager/resource/object/list" tags:"Backup-Manager" method:"get" summarg:"list backup resource obj"`
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Group     string `json:"group"`
	Resource  string `json:"resource"`
	Search    string `json:"search"`
	Page      int    `json:"page" d:"1"`
	Size      int    `json:"size" d:"10"`
}

// CreateBackupStrategyReq 创建备份策略
type CreateBackupStrategyReq struct {
	g.Meta       `path:"/backup-manager/backup-strategy/create" tags:"Backup-Manager" method:"post" summarg:"create backup resource strategy"`
	ClusterId    int      `json:"clusterId"`
	Group        string   `json:"group"`
	Resource     string   `json:"resource"`
	Namespace    string   `json:"namespace"`
	ObjectNames  []string `json:"objectNames"`
	IsPersistent bool     `json:"isPersistent"`
	Desc         string   `json:"desc"`
}

// DeleteBackupStrategyReq 删除备份策略
type DeleteBackupStrategyReq struct {
	g.Meta `path:"/backup-manager/backup-strategy/delete/:pid" tags:"Backup-Manager" method:"delete" summarg:"delete backup resource strategy"`
}

// ExecuteBackupStrategyReq 执行备份策略(相当于触发备份任务)
type ExecuteBackupStrategyReq struct {
	g.Meta `path:"/backup-manager/backup-strategy/execute" tags:"Backup-Manager" method:"post" summarg:"execute backup resource strategy"`
	Pid    int `json:"pid"`
}

// ListBackupStrategyTaskReq 查询备份策略关联的任务
type ListBackupStrategyTaskReq struct {
	g.Meta `path:"/backup-manager/strategy/task/list" tags:"Backup-Manager" method:"get" summarg:"list backup resource task"`
	Pid    int `json:"pid"`
	Page   int `json:"page" d:"1"`
	Size   int `json:"size" d:"10"`
}

// ListBackupRecoverStrategyReq 查询备份/恢复策略
type ListBackupRecoverStrategyReq struct {
	g.Meta    `path:"/backup-manager/strategy/list" tags:"Backup-Manager" method:"get" summarg:"list backup resource strategy"`
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Group     string `json:"group"`
	Resource  string `json:"resource"`
	Mode      string `json:"mode"` // backup | recover
	Page      int    `json:"page" d:"1"`
	Size      int    `json:"size" d:"10"`
}

// CreateRecoverStrategyReq 创建恢复策略
type CreateRecoverStrategyReq struct {
	g.Meta      `path:"/backup-manager/recover-strategy/create" tags:"Backup-Manager" method:"post" summarg:"create recover resource strategy"`
	ClusterId   int      `json:"clusterId"`
	Group       string   `json:"group"`
	Resource    string   `json:"resource"`
	Namespace   string   `json:"namespace"`
	ObjectNames []string `json:"objectIds"`
}

// DeleteRecoverStrategyReq 删除恢复策略
type DeleteRecoverStrategyReq struct {
	g.Meta `path:"/backup-manager/recover-strategy/delete/:pid" tags:"Backup-Manager" method:"delete" summarg:"delete recover resource strategy"`
}

// ExecuteRecoverStrategyReq 执行恢复策略(相当于触发备份任务)
type ExecuteRecoverStrategyReq struct {
	g.Meta `path:"/backup-manager/recover-strategy/execute" tags:"Backup-Manager" method:"post" summarg:"execute recover resource strategy"`
	Pid    int `json:"pid"`
}

// ListBackupRecoverStrategyClusterReq 根据备份表获取恢复策略中的集群数据
type ListBackupRecoverStrategyClusterReq struct {
	g.Meta `path:"/backup-manager/recover-strategy/cluster/list" tags:"Backup-Manager" method:"get" summarg:"list backup resource strategy cluster"`
}

// ListBackupRecoverStrategyGrReq 根据备份表和集群id获取恢复策略中的gr
type ListBackupRecoverStrategyGrReq struct {
	g.Meta    `path:"/backup-manager/recover-strategy/gr/list" tags:"Backup-Manager" method:"get" summarg:"list backup resource strategy gr"`
	ClusterId int `json:"clusterId"`
}

// ListBackupRecoverStrategyNamespaceReq 根据备份表和集群id获取恢复策略中的namespace
type ListBackupRecoverStrategyNamespaceReq struct {
	g.Meta    `path:"/backup-manager/recover-strategy/namespace/list" tags:"Backup-Manager" method:"get" summarg:"list backup resource strategy namespace"`
	ClusterId int `json:"clusterId"`
}

// ListBackupRecoverStrategyResourceReq 根据备份表、集群id、gr和namespace获取恢复策略中的资源对象,为空即所有 list
type ListBackupRecoverStrategyResourceReq struct {
	g.Meta    `path:"/backup-manager/recover-strategy/resource/list" tags:"Backup-Manager" method:"get" summarg:"list backup resource strategy resource"`
	ClusterId int    `json:"clusterId"`
	Group     string `json:"group"`
	Namespace string `json:"namespace"`
	Resource  string `json:"resource"`
	Search    string `json:"search"`
	Page      int    `json:"page" d:"1"`
	Size      int    `json:"size" d:"10"`
}

// ListBackupStrategyTaskObjectReq 查询备份任务关联的具体信息
type ListBackupStrategyTaskObjectReq struct {
	g.Meta     `path:"/backup-manager/strategy/task/object/list" tags:"Backup-Manager" method:"get" summarg:"list backup resource task"`
	StrategyId int    `json:"strategyId"`
	TaskId     int    `json:"taskId"`
	Search     string `json:"search"`
	Page       int    `json:"page" d:"1"`
	Size       int    `json:"size" d:"10"`
}

type ListBackupStrategyTaskObjectRes struct {
	Total        int         `json:"total"`
	Page         int         `json:"page"`
	Size         int         `json:"size"`
	Count        int         `json:"count"`
	SuccessCount int         `json:"successCount"`
	FailCount    int         `json:"failCount"`
	List         interface{} `json:"list"`
}
