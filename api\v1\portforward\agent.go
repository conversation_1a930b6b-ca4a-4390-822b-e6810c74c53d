package portforward

import (
	"github.com/gogf/gf/v2/frame/g"
	dto "tt-cloud-enterprise/internal/model/dto/portforward"
)

type StatusReq struct {
	g.Meta     `path:"/portforward/agent/status/upload" method:"post" summary:"上传端口转发状态" tags:"portforward"`
	Cluster    string                `json:"cluster"`
	Namespace  string                `json:"namespace"`
	Pod        string                `json:"pod"`
	TargetPort int                   `json:"targetPort"`
	ProxyPort  int                   `json:"proxyPort"`
	Status     dto.PortForwardStatus `json:"status"`
}

type FreePortReq struct {
	g.Meta `path:"/portforward/agent/free-port/get" method:"get" summary:"获取空闲端口" tags:"portforward"`
}

type FreePortRes struct {
	dto.FreePort
}

type ListPortForwardReq struct {
	g.Meta `path:"/portforward/agent/list" method:"get" summary:"获取端口转发列表" tags:"portforward"`
}

type ListPortForwardRes struct {
	Data []*dto.AgentForwardPort `json:"data"`
}
