package resource

import "github.com/gogf/gf/v2/frame/g"

type ListResourceConfigmapReq struct {
	g.Meta    `path:"/resource/configmap/list" tags:"configmap" method:"get" summary:"list configmap" auth:"k8s-namespaceConfigmap-r"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Search    string `json:"search"  dc:"search"`
	Page      int    `json:"page" d:"1"`
	Size      int    `json:"size" d:"10"`
}

type GetResourceConfigmapDetailReq struct {
	g.Meta    `path:"/resource/configmap/get" tags:"configmap" method:"get" summary:"get configmap detail" auth:"k8s-namespaceConfigmap-r"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Name      string `json:"name" v:"required#Require name."`
}

type CreateResourceConfigmapReq struct {
	g.Meta    `path:"/resource/configmap/yaml/create" tags:"configmap" method:"post" summary:"create configmap" auth:"k8s-namespaceConfigmap-w"`
	ClusterId int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                 `json:"namespace" v:"required#Require namespace."`
	Data      map[string]interface{} `json:"data" v:"required#Require data."`
}

type DeleteResourceConfigmapReq struct {
	g.Meta    `path:"/resource/configmap/delete" tags:"configmap" method:"delete" summary:"delete configmap" auth:"k8s-namespaceConfigmap-w"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Name      string `json:"name" v:"required#Require name."`
}

type UpdateResourceConfigmapReq struct {
	g.Meta    `path:"/resource/configmap/yaml/update" tags:"configmap" method:"post" summary:"update configmap" auth:"k8s-namespaceConfigmap-w"`
	ClusterId int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                 `json:"namespace" v:"required#Require namespace."`
	Data      map[string]interface{} `json:"name" v:"required#Require data."`
}
