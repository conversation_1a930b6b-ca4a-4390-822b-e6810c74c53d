package resource

import (
	"github.com/gogf/gf/v2/frame/g"

	"tt-cloud-enterprise/internal/consts/praconsts"
)

type ListResourceServiceReq struct {
	g.Meta    `path:"/resource/service/list" tags:"resource" method:"get" summary:"list resource service"`
	ClusterID int    `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace string `json:"namespace" v:"required#Require namespace." dc:"namespace"`
	Search    string `json:"search"  dc:"search"`
	Page      int    `json:"page" d:"1"`
	Size      int    `json:"size" d:"10"`
}

type GetResourceServiceReq struct {
	g.Meta    `path:"/resource/service/:uuid" tags:"resource" method:"get" summary:"get resource service "`
	ClusterId int    `json:"cluster_id" v:"required#Require cluster_id."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
}

type GetResourceRelatedServicePodReq struct {
	g.Meta    `path:"/resource/related/service/pod" tags:"resource" method:"get" summary:"get resource related service pod "`
	Uuid      string `json:"uuid" v:"required#Require uuid." dc:"service uuid"`
	ClusterID int    `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace string `json:"namespace" v:"required#Require namespace." dc:"namespace"`
}

// TODO: 暂定先不使用 auth鉴别接口权限
type GoToResourceRelatedServiceDeploymentReq struct {
	g.Meta    `path:"/resource/related/service/goto-deployment" tags:"resource" method:"post" summary:"resource related service goto-deployment "`
	ClusterID int    `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace string `json:"namespace" v:"required#Require namespace." dc:"namespace"`
	// pod name
	PodName string `json:"pod_name"`
}

// 校验用户svc权限
type CheckServiceResourceAuthReq struct {
	g.Meta    `path:"/resource/service/auth/check" tags:"resource" method:"get" summary:"check svc resource auth "`
	ClusterId int                 `json:"cluster_id"`
	Namespace string              `json:"namespace"`
	Auth      praconsts.Authority `json:"auth"`
}

type ListResourceServiceFQDNReq struct {
	g.Meta    `path:"/resource/service/fqdn/list" tags:"resource" method:"get" summary:"get resource service "`
	ClusterId int `json:"clusterId" v:"required#Require clusterId."`
}

type GetResourceServicePortNumberByHostReq struct {
	g.Meta    `path:"/resource/service/fqdn/port-number/get" tags:"resource" method:"get" summary:"get resource service "`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Host      string `json:"host" v:"required#Require host."`
}

type GetResourceServiceRelatedDestiantionRuleSubsetByHostReq struct {
	g.Meta    `path:"/resource/service/fqdn/related/destinationrule/subset/get" tags:"resource" method:"get" summary:"get resource service "`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Host      string `json:"host" v:"required#Require host."`
}
