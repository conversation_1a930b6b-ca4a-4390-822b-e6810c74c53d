package resource

import "github.com/gogf/gf/v2/frame/g"

type ListResourceSecretReq struct {
	g.Meta    `path:"/resource/secret/list" tags:"secret" method:"get" summary:"list secret" auth:"k8s-namespaceSecret-r"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Search    string `json:"search"  dc:"search"`
	Page      int    `json:"page" d:"1"`
	Size      int    `json:"size" d:"10"`
}

type GetResourceSecretDetailReq struct {
	g.Meta    `path:"/resource/secret/get" tags:"secret" method:"get" summary:"get secret detail" auth:"k8s-namespaceSecret-r"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Name      string `json:"name" v:"required#Require name."`
}

type CreateResourceSecretReq struct {
	g.Meta    `path:"/resource/secret/yaml/create" tags:"secret" method:"post" summary:"create secret" auth:"k8s-namespaceSecret-w"`
	ClusterId int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                 `json:"namespace" v:"required#Require namespace."`
	Data      map[string]interface{} `json:"data" v:"required#Require data."`
}
type DeleteResourceSecretReq struct {
	g.Meta    `path:"/resource/secret/delete" tags:"secret" method:"delete" summary:"delete secret" auth:"k8s-namespaceSecret-w"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Name      string `json:"name" v:"required#Require name."`
}

type UpdateResourceSecretReq struct {
	g.Meta    `path:"/resource/secret/yaml/update" tags:"secret" method:"post" summary:"update secret" auth:"k8s-namespaceSecret-w"`
	ClusterId int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                 `json:"namespace" v:"required#Require namespace."`
	Data      map[string]interface{} `json:"data" v:"required#Require data."`
}

type ListResourceSecretTlsReq struct {
	g.Meta    `path:"/resource/secret/tls/list" tags:"secret" method:"get" summary:"list secret" auth:"k8s-namespaceSecret-r"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
}
