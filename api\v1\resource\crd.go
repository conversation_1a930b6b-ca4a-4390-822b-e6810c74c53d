package resource

import (
	"github.com/gogf/gf/v2/frame/g"
	k8s2 "gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/utils/k8s"
)

type ListResourceCrdReq struct {
	g.Meta    `path:"/resource/crd/list" tags:"crd" method:"get" summary:"list crd"`
	ClusterId int `json:"clusterId" v:"required#Require clusterId."`
}

type ListResourceCrdObjReq struct {
	g.Meta     `path:"/resource/crd/object/list" tags:"crd" method:"get" summary:"list crd object"`
	ClusterId  int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace  string `json:"namespace"`
	Group      string `json:"group"`
	Version    string `json:"version"`
	Resource   string `json:"resource"`
	Search     string `json:"search"`
	Namespaced bool   `json:"namespaced"`
	Name       string `json:"name"`
	Page       int    `json:"page" d:"1"`
	Size       int    `json:"size" d:"10"`
}

type ListResourceCrdObjRes struct {
	Cols  []k8s2.ResourcesColumns `json:"cols"`
	Total int                     `json:"total"`
	Page  int                     `json:"page"`
	Size  int                     `json:"size"`
	List  interface{}             `json:"list"`
}

type DeleteResourceCrdObjReq struct {
	g.Meta    `path:"/resource/crd/object/delete" tags:"crd" method:"delete" summary:"delete crd object" auth:"k8s-namespaceCrd-w"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace"`
	Name      string `json:"name" v:"required#Require name."`
	Group     string `json:"group" v:"required#Require group."`
	Version   string `json:"version" v:"required#Require version."`
	Resource  string `json:"resource" v:"required#Require resource."`
}

type CreateResourceCrdObjYamlReq struct {
	g.Meta    `path:"/resource/crd/object/yaml/create" tags:"crd" method:"post" summary:"create crd object with yaml" auth:"k8s-namespaceCrd-w"`
	ClusterId int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                 `json:"namespace"`
	Data      map[string]interface{} `json:"data" v:"required#Require name."`
}

type UpdateResourceCrdObjYamlReq struct {
	g.Meta    `path:"/resource/crd/object/yaml/update" tags:"crd" method:"post" summary:"update crd object with yaml" auth:"k8s-namespaceCrd-w"`
	ClusterId int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                 `json:"namespace"`
	Data      map[string]interface{} `json:"data" v:"required#Require name."`
}

type GetResourceCrdTableReq struct {
	g.Meta     `path:"/resource/crd/table" tags:"crd" method:"get" summary:"get crd table"`
	ClusterId  int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace  string `json:"namespace"`
	Group      string `json:"group" v:"required#Require group."`
	Version    string `json:"version" v:"required#Require version."`
	Resource   string `json:"resource" v:"required#Require resource."`
	Namespaced bool   `json:"namespaced"`
}
