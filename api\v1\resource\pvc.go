package resource

import "github.com/gogf/gf/v2/frame/g"

type ListResourcePvcReq struct {
	g.Meta    `path:"/resource/pvc/list" tags:"pvc" method:"get" summary:"list pvc" auth:"k8s-namespacePVC-r"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Search    string `json:"search"  dc:"search"`
	Page      int    `json:"page" d:"1"`
	Size      int    `json:"size" d:"10"`
}

type CreateResourcePvcWithYamlReq struct {
	g.Meta    `path:"/resource/pvc/yaml/create" tags:"pvc" method:"post" summary:"create pvc with yaml" auth:"k8s-namespacePVC-w"`
	ClusterId int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                 `json:"namespace" v:"required#Require namespace."`
	Data      map[string]interface{} `json:"data" v:"required#Require data."`
}

type UpdateResourcePvcWithYamlReq struct {
	g.Meta    `path:"/resource/pvc/yaml/update" tags:"pvc" method:"post" summary:"update pvc with yaml" auth:"k8s-namespacePVC-w"`
	ClusterId int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                 `json:"namespace" v:"required#Require namespace."`
	Data      map[string]interface{} `json:"data" v:"required#Require data."`
}

type DeleteResourcePvcReq struct {
	g.Meta    `path:"/resource/pvc/delete" tags:"pvc" method:"delete" summary:"delete pvc" auth:"k8s-namespacePVC-w"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Name      string `json:"name" v:"required#Require name."`
}
