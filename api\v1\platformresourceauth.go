package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"tt-cloud-enterprise/internal/consts/praconsts"
	"tt-cloud-enterprise/internal/model/dto"
	"tt-cloud-enterprise/internal/model/entity"
)

// PRAListK8sAuthReq 列出所有k8s权限
type PRAListK8sAuthReq struct {
	g.Meta    `path:"/pr-auth/authority/k8s/list" tags:"PlatformResourceAuth" method:"get" summary:"列出所有k8s权限"`
	ClusterId int                 `json:"cluster_id" v:"required#Require k8s cluster id"`
	Namespace string              `json:"namespace"`
	Kind      praconsts.K8sRsKind `json:"kind" v:"required#Require k8s kind" desc:"k8s 资源种类"`
}

type PRAListK8sAuthRes struct {
	Data []entity.PrAuthK8SAuth `json:"data"`
}

// PRAListViewerAuthReq 列出所有 viewer 权限
type PRAListViewerAuthReq struct {
	g.Meta `path:"/pr-auth/authority/viewer/list" tags:"PlatformResourceAuth" method:"get" summary:"列出所有 viewer 权限"`
}

type PRAListViewerAuthRes struct {
	Data []entity.PrAuthViewerAuth `json:"data"`
}

// PRAGetK8sAuthWithUserReq 列出单个用户的 k8s 权限
type PRAGetK8sAuthWithUserReq struct {
	g.Meta    `path:"/pr-auth/authority/k8s-with-user/get/:uid" tags:"PlatformResourceAuth" method:"get" summary:"列出单个用户的 k8s 权限"`
	ClusterId int                 `json:"cluster_id" v:"required#Require cluster id"`
	Kind      praconsts.K8sRsKind `json:"kind" v:"required#Require kind"`
	Namespace string              `json:"namespace" v:"required#Require namespace"`
}

type PRAGetK8sAuthWithUserRes struct {
	Data []entity.PrAuthK8SAuth `json:"data"`
}

// PRAGetViewerAuthWithUserReq 列出单个用户的 viewer 权限
type PRAGetViewerAuthWithUserReq struct {
	g.Meta `path:"/pr-auth/authority/viewer-with-user/get/:uid" tags:"PlatformResourceAuth" method:"get" summary:"列出单个用户的 viewer 权限"`
}

type PRAGetViewerAuthWithUserRes struct {
	Data []entity.PrAuthViewerAuth `json:"data"`
}

// PRAGetRoleAuthWithUserReq 列出单个用户的所有角色和权限
type PRAGetRoleAuthWithUserReq struct {
	g.Meta `path:"/pr-auth/role/user/get/:uid" tags:"PlatformResourceAuth" method:"get" summary:"列出单个用户的所有角色和权限"`
}

type PRAGetRoleAuthWithUserRes struct {
	Data []dto.PRRoleWithAuth `json:"data"`
}

// PRAUpdateK8sRoleAuthWithUserReq 更新或创建用户 k8s 角色权限
type PRAUpdateK8sRoleAuthWithUserReq struct {
	g.Meta `path:"/pr-auth/role/k8s-with-user/update/:uid" tags:"PlatformResourceAuth" method:"post" summary:"更新或创建用户 k8s 角色权限" auth:"management-user-w"`
	AuthId []string `json:"auth_id" v:"required#Require authority id" desc:"k8s 权限id"`
}

// PRAUpdateViewerRoleAuthWithUserReq 更新或创建用户 viewer 角色权限
type PRAUpdateViewerRoleAuthWithUserReq struct {
	g.Meta `path:"/pr-auth/role/viewer-with-user/update/:uid" tags:"PlatformResourceAuth" method:"post" summary:"更新或创建用户 viewer 角色权限" auth:"management-user-w"`
	AuthId []int `json:"auth_id" v:"required#Require authority id" desc:"viewer 权限id"`
}

// PRADeleteK8sRoleAuthWithUserReq 删除用户 k8s 角色的权限
type PRADeleteK8sRoleAuthWithUserReq struct {
	g.Meta `path:"/pr-auth/role/k8s-with-user/delete/:uid" tags:"PlatformResourceAuth" method:"delete" summary:"删除用户 k8s 角色的权限" auth:"management-user-w"`
	AuthId []string `json:"auth_id" v:"required#Require authority id" desc:"k8s 权限id"`
}

// PRADeleteViewerRoleAuthWithUserReq 删除用户 viewer 角色的权限
type PRADeleteViewerRoleAuthWithUserReq struct {
	g.Meta `path:"/pr-auth/role/viewer-with-user/delete/:uid" tags:"PlatformResourceAuth" method:"delete" summary:"删除用户 viewer 角色的权限" auth:"management-user-w"`
	AuthId []int `json:"auth_id" v:"required#Require authority id" desc:"viewer 权限id"`
}

// PRAGenerateK8sAuthReq 生成k8s权限
type PRAGenerateK8sAuthReq struct {
	g.Meta `path:"/pr-auth/authority/k8s/generate-auth" tags:"PlatformResourceAuth" method:"post" summary:"生成k8s权限" auth:"k8s-managementConfig-w"`
}

// PRAClearK8sAuthReq 清除 k8s 集群关联的失效权限
type PRAClearK8sAuthReq struct {
	g.Meta    `path:"/pr-auth/authority/k8s/clear" tags:"PlatformResourceAuth" method:"post" summary:"清除 k8s 集群关联的失效权限" auth:"k8s-managementConfig-w"`
	ClusterId int `json:"cluster_id" v:"required#Require cluster id"`
}

// PRACheckK8sAuthReq 检测 k8s 资源权限
type PRACheckK8sAuthReq struct {
	g.Meta    `path:"/pr-auth/authority/k8s/check" tags:"PlatformResourceAuth" method:"post" summary:"检测 k8s 资源权限"`
	ClusterId int                 `json:"cluster_id" v:"required#Require cluster id"`
	Namespace string              `json:"namespace" v:"required#Require namespace"`
	Kind      praconsts.K8sRsKind `json:"kind" v:"required#Require kind"`
	Auth      praconsts.Authority `json:"auth" v:"required#Require auth (r|w|x)"`
	Resource  string              `json:"resource"`
}

// PRACreateNamespaceAuthReq 获取命名空间下的统一授权
type PRAGetNamespaceAuthReq struct {
	g.Meta    `path:"/pr-auth/authority/namespace-auth/get/:uid" tags:"PlatformResourceAuth" method:"get" summary:"获取命名空间下的统一授权"`
	ClusterId int    `json:"cluster_id" v:"required#Require cluster id"`
	Namespace string `json:"namespace" `
}

type PRAGetNamespaceAuthRes struct {
	W bool `json:"w"`
	R bool `json:"r"`
	X bool `json:"x"`
}

// PRACreateNamespaceAuth 创建单个命名空间下的读写执行权限
type PRACreateNamespaceAuthReq struct {
	g.Meta    `path:"/pr-auth/authority/namespace-auth/create" tags:"PlatformResourceAuth" method:"post" summary:"创建单个命名空间下的读写执行权限" auth:"management-user-w"`
	ClusterId int                 `json:"cluster_id" v:"required#Require cluster id"`
	Namespace string              `json:"namespace" v:"required#Require namespace"`
	Auth      praconsts.Authority `json:"auth" v:"required#Require auth (r|w|x)"`
	Uid       int                 `json:"uid"`
}

// PRACreateNamespaceAuth 删除单个命名空间下的读写执行权限
type PRADeleteNamespaceAuthReq struct {
	g.Meta    `path:"/pr-auth/authority/namespace-auth/delete" tags:"PlatformResourceAuth" method:"delete" summary:"创建单个命名空间下的读写执行权限" auth:"management-user-w"`
	ClusterId int                 `json:"cluster_id" v:"required#Require cluster id"`
	Namespace string              `json:"namespace" v:"required#Require namespace"`
	Auth      praconsts.Authority `json:"auth" v:"required#Require auth (r|w|x)"`
	Uid       int                 `json:"uid"`
}

// PRARelocationPermissionReq 迁移namespace权限
type PRARelocationPermissionReq struct {
	g.Meta `path:"/pr-auth/authority/k8s/relocation" tags:"PlatformResourceAuth" method:"post" summary:"迁移老版本的 k8s 资源权限"`
}

// PRAAddViewAuthReq 给所有用户补充页面权限
type PRAAddViewAuthReq struct {
	g.Meta   `path:"/pr-auth/authority/view/add" tags:"PlatformResourceAuth" method:"post" summary:"add view auth"`
	AuthName string `json:"auth_name"`
}

// CopyPlatformResourceAuthReq 为用户复制平台新资源权限
type CopyPlatformResourceAuthReq struct {
	g.Meta      `path:"/pr-auth/authority/resource/copy" tags:"PlatformResourceAuth" method:"post" summary:"复制用户平台新资源权限" auth:"management-user-w"`
	UserId      int      `json:"userId"`
	OriginKinds []string `json:"originKinds"`
	TargetKind  string   `json:"targetKind"`
	ViewNameR   string   `json:"viewNameR"`
	ViewNameW   string   `json:"viewNameW"`
}

// CopyPlatformResourceAuthWithAllUserReq 为所有用户复制平台新资源权限
type CopyPlatformResourceAuthWithAllUserReq struct {
	g.Meta      `path:"/pr-auth/authority/resource/all-user/copy" tags:"PlatformResourceAuth" method:"post" summary:"复制所有用户平台新资源权限" auth:"management-user-w"`
	OriginKinds []string `json:"originKinds"`
	TargetKind  string   `json:"targetKind"`
	ViewNameR   string   `json:"viewNameR"`
	ViewNameW   string   `json:"viewNameW"`
}

type ManageViewAuthWithKindReq struct {
	g.Meta      `path:"/pr-auth/manage/view/kind/bind" tags:"PlatformResourceAuth" method:"post" summary:"bind view auth with kind"`
	Kind        string `json:"kind" v:"required#Require kind"`
	UserIds     []int  `json:"userIds"`
	PassAuthIds []int  `json:"passAuthIds"`
}

type ManageViewAuthWithAuthReq struct {
	g.Meta  `path:"/pr-auth/manage/view/auth-id/bind" tags:"PlatformResourceAuth" method:"post" summary:"bind view auth with ids"`
	AuthIds []int `json:"authIds" v:"required#Require auth ids"`
	UserIds []int `json:"userIds"`
}

type ManageUnbindViewAuthWithAuthReq struct {
	g.Meta  `path:"/pr-auth/manage/view/auth-id/unbind" tags:"PlatformResourceAuth" method:"delete" summary:"bind view auth with ids"`
	AuthIds []int `json:"authIds" v:"required#Require auth ids"`
	UserIds []int `json:"userIds"`
}

type ManageAutoRefreshViewAuthReq struct {
	g.Meta  `path:"/pr-auth/manage/view/auto-refresh" tags:"PlatformResourceAuth" method:"post" summary:"bind view auth with ids"`
	UserIds []int `json:"userIds"`
}

type ManageAutoSyncCKAuthToPrAuthReq struct {
	g.Meta  `path:"/ck-auth/manage/auto-sync" tags:"PlatformResourceAuth" method:"post" summary:"Sync ck-auth To pr-auth"`
	UserIds []int `json:"userIds"`
}

type ManageSyncCKAuthFromOriginClusterReq struct {
	g.Meta        `path:"/ck-auth/manage/sync/origin-cluster" tags:"PlatformResourceAuth" method:"post" summary:"Sync ck-auth from origin cluster"`
	UserIds       []int  `json:"userIds"`
	OriginCluster string `json:"originCluster" v:"required#Require origin cluster"`
	TargetCluster string `json:"targetCluster" v:"required#Require target cluster"`
}

type ManageSyncPrAuthFromOriginClusterReq struct {
	g.Meta        `path:"/pr-auth/manage/sync/origin-cluster" tags:"PlatformResourceAuth" method:"post" summary:"Sync pr-auth from origin cluster"`
	UserIds       []int    `json:"userIds"`
	OriginCluster string   `json:"originCluster" v:"required#Require origin cluster"`
	TargetCluster string   `json:"targetCluster" v:"required#Require target cluster"`
	Namespaces    []string `json:"namespaces"`
}

type ManageRebuildTargetClusterAuthReq struct {
	g.Meta        `path:"/pr-auth/manage/rebuild" tags:"PlatformResourceAuth" method:"post" summary:"rebuild target cluster auth"`
	UserIds       []int  `json:"userIds"`
	TargetCluster string `json:"targetCluster" v:"required#Require target cluster"`
}
