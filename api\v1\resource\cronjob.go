package resource

import "github.com/gogf/gf/v2/frame/g"

type ListResourceCronjobReq struct {
	g.Meta    `path:"/resource/cronjob/list" tags:"job" method:"get" summary:"list cronjob"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Search    string `json:"search"`
	Page      int    `json:"page" d:"1"`
	Size      int    `json:"size" d:"10"`
}

type GetResourceCronjobDetailReq struct {
	g.Meta    `path:"/resource/cronjob/detail" tags:"job" method:"get" summary:"list cronjob"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Name      string `json:"name" v:"required#Require name."`
}

type ListResourceCronJobRelatedPodReq struct {
	g.Meta    `path:"/resource/cronjob/related/pod" tags:"job" method:"get" summary:"list cronjob related pod"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Name      string `json:"name" v:"required#Require name."`
}

type ListResourceCronJobRelatedJobReq struct {
	g.Meta    `path:"/resource/cronjob/related/job" tags:"job" method:"get" summary:"list cronjob related job"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Name      string `json:"name" v:"required#Require name."`
}

type DeleteResourceCronjobReq struct {
	g.Meta    `path:"/resource/cronjob/delete" tags:"job" method:"delete" summary:"delete cronjob" auth:"k8s-namespaceCronjob-w"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace"`
	Name      string `json:"name" v:"required#Require name."`
}

type CreateResourceCronjobYamlReq struct {
	g.Meta    `path:"/resource/cronjob/yaml/create" tags:"job" method:"post" summary:"create cronjob object with yaml" auth:"k8s-namespaceCronjob-w"`
	ClusterId int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                 `json:"namespace"`
	Data      map[string]interface{} `json:"data" v:"required#Require name."`
}

type UpdateResourceCronjobYamlReq struct {
	g.Meta    `path:"/resource/cronjob/yaml/update" tags:"job" method:"post" summary:"update cronjob object with yaml" auth:"k8s-namespaceCronjob-w"`
	ClusterId int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                 `json:"namespace"`
	Data      map[string]interface{} `json:"data" v:"required#Require name."`
}
