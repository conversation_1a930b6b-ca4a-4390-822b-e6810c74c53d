package transparent

import (
	"github.com/gogf/gf/v2/frame/g"
)

type DownloadContainerFileReq struct {
	g.Meta    `path:"/container/file/download" tags:"pods" method:"get" summary:"download file in container"`
	ClusterId int    `json:"clusterId" v:"required#Require cluster."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Pod       string `json:"pod" v:"required#Require pod."`
	Container string `json:"container" v:"required#Require container."`
	FilePath  string `json:"filePath" v:"required#Require file path."`
}

type UploadFileToContainerReq struct {
	g.Meta    `path:"/container/file/upload" tags:"pods" method:"post" summary:"download file in container"`
	ClusterId int    `json:"clusterId" v:"required#Require cluster."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Pod       string `json:"pod" v:"required#Require pod."`
	Container string `json:"container" v:"required#Require container."`
	DestFilePath  string `json:"destFilePath" v:"required#Require dest file path."`
}
