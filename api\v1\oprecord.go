package v1

import "github.com/gogf/gf/v2/frame/g"

//type ListOpRecordReq struct {
//	g.Meta `path:"/op-record/list" tags:"OpRecord" method:"get" summary:"operation record list"`
//	ListReq
//}

type ListOpRecordV2Req struct {
	g.Meta         `path:"/op-record/list" tags:"OpRecord" method:"get" summary:"operation record list"`
	Page           int    `json:"page" d:"1"`  // 分页号码
	Size           int    `json:"size" d:"10"` // 分页数量
	UserName       string `json:"userName"`
	Method         string `json:"method"`
	Code           *int   `json:"code"`
	Uri            string `json:"url"`
	StartTimeStamp int64  `json:"startTimeStamp"`
	EndTimeStamp   int64  `json:"endTimeStamp"`
	QueryParams    string `json:"queryParams"`
	QueryBody      string `json:"queryBody"`
}

type ListOpRecordOptReq struct {
	g.Meta `path:"/op-record/opt/list" tags:"OpRecord" method:"get" summary:"operation record opt list"`
}

// SortType   string `json:"sortType" d:"desc"` // asc | desc}

// SortType   string `json:"sortType" d:"desc"` // asc | desc}
