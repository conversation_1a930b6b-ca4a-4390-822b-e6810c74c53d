package resource

import (
	"github.com/gogf/gf/v2/frame/g"
	"tt-cloud-enterprise/internal/model/dto/k8s"
	uModel "tt-cloud-enterprise/utility/k8s/model"
)

type ListResourceHpaReq struct {
	g.Meta    `path:"/resource/hpa/list" tags:"hpa" method:"get" summary:"list hpa" auth:"k8s-namespaceHpa-r" `
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Search    string `json:"search"  dc:"search"`
	Page      int    `json:"page" d:"1"`
	Size      int    `json:"size" d:"10"`
}

type GetResourceHpaDetailReq struct {
	g.Meta    `path:"/resource/hpa/get" tags:"hpa" method:"get" summary:"get hpa detail" auth:"k8s-namespaceHpa-r" `
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Name      string `json:"name" v:"required#Require name."`
}

type GetResourceHpaFormReq struct {
	g.Meta    `path:"/resource/hpa/form/get" tags:"hpa" method:"get" summary:"get hpa form" auth:"k8s-namespaceHpa-r" `
	ClusterId int         `json:"clusterId" v:"required#Require clusterId."`
	Namespace string      `json:"namespace" v:"required#Require namespace."`
	Name      string      `json:"name" v:"required#Require name."`
	Kind      k8s.HpaKind `json:"kind" v:"required#Require kind."`
}

type GetResourceHpaDetailRes struct {
	Data           interface{}            `json:"data"`
	Target         []uModel.HpaTarget     `json:"target"`
	ScaleTargetRef *uModel.ScaleTargetRef `json:"scaleTargetRef"`
}

type CreateResourceHpaReq struct {
	g.Meta    `path:"/resource/hpa/yaml/create" tags:"hpa" method:"post" summary:"create hpa" auth:"k8s-namespaceHpa-w" `
	ClusterId int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                 `json:"namespace" v:"required#Require namespace."`
	Data      map[string]interface{} `json:"data" v:"required#Require data."`
}

type UpdateResourceHpaReq struct {
	g.Meta    `path:"/resource/hpa/yaml/update" tags:"hpa" method:"post" summary:"update hpa" auth:"k8s-namespaceHpa-w" `
	ClusterId int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                 `json:"namespace" v:"required#Require namespace."`
	Data      map[string]interface{} `json:"name" v:"required#Require data."`
}

type DeleteResourceHpaReq struct {
	g.Meta    `path:"/resource/hpa/delete" tags:"hpa" method:"delete" summary:"delete hpa" auth:"k8s-namespaceHpa-w"`
	ClusterId int         `json:"clusterId" v:"required#Require clusterId."`
	Namespace string      `json:"namespace" v:"required#Require namespace."`
	Name      string      `json:"name" v:"required#Require name."`
	Kind      k8s.HpaKind `json:"kind" v:"required#Require kind."`
}

type CreateResourceHpaWithFormReq struct {
	g.Meta    `path:"/resource/hpa/form/create" tags:"hpa" method:"post" summary:"create hpa with form" auth:"k8s-namespaceHpa-w"`
	ClusterId int         `json:"clusterId" v:"required#Require clusterId."`
	Namespace string      `json:"namespace" v:"required#Require namespace."`
	HpaForm   k8s.HpaForm `json:"hpaForm" v:"required#Require hpaForm."`
}

type UpdateResourceHpaWithFormReq struct {
	g.Meta    `path:"/resource/hpa/form/update" tags:"hpa" method:"post" summary:"update hpa with form" auth:"k8s-namespaceHpa-w"`
	ClusterId int         `json:"clusterId" v:"required#Require clusterId."`
	Namespace string      `json:"namespace" v:"required#Require namespace."`
	Kind      k8s.HpaKind `json:"kind" v:"required#Require kind."`
	HpaForm   k8s.HpaForm `json:"hpaForm" v:"required#Require hpaForm."`
}

type CheckResourceHpaIsConvertReq struct {
	g.Meta    `path:"/resource/hpa/form/is-convert" tags:"hpa" method:"get" summary:"check hpa convert" `
	ClusterId int         `json:"clusterId" v:"required#Require clusterId."`
	Namespace string      `json:"namespace" v:"required#Require namespace."`
	Name      string      `json:"name" v:"required#Require name." `
	Kind      k8s.HpaKind `json:"kind" v:"required#Require kind."`
}

// CheckHpaSupportKedaReq 在当前集群对hpa开启定时伸缩
type CheckHpaSupportKedaReq struct {
	g.Meta    `path:"/resource/hpa/keda/is-support" tags:"hpa" method:"get" summary:"check support keda" `
	ClusterId int `json:"clusterId" v:"required#Require clusterId."`
}

// ListWorkloadNameWithKindReq workload列表
type ListWorkloadNameWithKindReq struct {
	g.Meta    `path:"/resource/hpa/form/workload-name/list" tags:"hpa" method:"get" summary:"list workload with kind" `
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Kind      string `json:"kind" v:"required#Require kind."`
}

// ListTimeZoneReq 时区列表
type ListTimeZoneReq struct {
	g.Meta `path:"/resource/hpa/timezone/list" tags:"hpa" method:"get" summary:"list timezone" `
}
