package resource

import (
	"github.com/gogf/gf/v2/frame/g"
	"tt-cloud-enterprise/internal/consts/praconsts"
	k8s2 "tt-cloud-enterprise/internal/model/dto/k8s"
)

//------------------------------------------------pod-----------------------------------

// 校验用户pod权限
type CheckPodResourceAuthReq struct {
	g.Meta    `path:"/resource/pod/auth/check" tags:"pods" method:"get" summary:"check pod resource auth "`
	ClusterId int                 `json:"cluster_id"`
	Namespace string              `json:"namespace"`
	Auth      praconsts.Authority `json:"auth"`
}

type GetResourcePodMetricsReq struct {
	g.Meta    `path:"/resource/pod/metrics/get" tags:"pods" method:"get" summary:"get deployments resource pod metrics" `
	ClusterId int    `json:"cluster_id"`
	Namespace string `json:"namespace"`
	PodName   string `json:"pod_name"`
}
type PodLogsListReq struct {
	g.<PERSON>a    `path:"/resource/pod/logs/list" tags:"pods" method:"get" summary:"pod logs list"`
	ClusterId int    `json:"cluster_id"`
	Namespace string `json:"namespace"`
	Pod       string `json:"pod"`
	Container string `json:"container"`
	Tail      int    `json:"tail" d:"100"`
}

type PodLogReq struct {
	g.Meta      `path:"/resource/pod/log/search" tags:"pods" method:"post" summary:"get pod logs"`
	ClusterId   int             `json:"clusterId"`
	Namespace   string          `json:"namespace"`
	Pod         string          `json:"pod"`
	Container   string          `json:"container"`
	Keywords    []string        `json:"keywords"`
	MatchType   *k8s2.MatchType `json:"matchType"`
	ContextLine int             `json:"contextLine"`
	Line        int             `json:"line" d:"100"`
}

// ListResourcePodReq pod列表
type ListResourcePodReq struct {
	g.Meta    `path:"/resource/pod/list" tags:"pods" method:"get" summary:"pod list" auth:"k8s-namespacePods-r"`
	ClusterId int    `json:"cluster_id" v:"required#Require cluster."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Status    string `json:"status"`
	Search    string `json:"search"  dc:"search"`
	Page      int    `json:"page" d:"1"`
	Size      int    `json:"size" d:"10"`
}

// GetResourcePodDetailReq 获取pod详情页
type GetResourcePodDetailReq struct {
	g.Meta    `path:"/resource/pod" tags:"pods" method:"get" summary:"get pod detail" auth:"k8s-namespacePods-r"`
	ClusterId int    `json:"cluster_id" v:"required#Require cluster."`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}

// GetResourcePodDetailRes pod详情返回
type GetResourcePodDetailRes struct {
	Pod interface{} `json:"pod"`
}

// CreateResourcePodReq 根据yaml创建pod
type CreateResourcePodReq struct {
	g.Meta    `path:"/resource/pod/create" tags:"pods" method:"post" summary:"create pod" auth:"k8s-namespacePods-w"`
	ClusterId int    `json:"cluster_id" v:"required#Require cluster."`
	Yaml      string `json:"yaml"`
}

// DeleteResourcePodReq 根据pod name 删除
type DeleteResourcePodReq struct {
	g.Meta    `path:"/resource/pod/delete" tags:"pods" method:"delete" summary:"delete pod" auth:"k8s-namespacePods-w"`
	ClusterId int    `json:"cluster_id" v:"required#Require cluster."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Name      string `json:"name" v:"required#Require name."`
}

// UpdateResourcePodReq 根据yaml修改pod
type UpdateResourcePodReq struct {
	g.Meta    `path:"/resource/pod/update" tags:"pods" method:"post" summary:"update pod" auth:"k8s-namespacePods-w"`
	ClusterId int    `json:"cluster_id" v:"required#Require cluster."`
	Yaml      string `json:"yaml"`
}

// ListPodMetricsReq 批量查看pods的metrics数据
type ListPodMetricsReq struct {
	g.Meta    `path:"/resource/pod-metrics/list" tags:"pods" method:"post" summary:"list pod metrics"`
	ClusterId int      `json:"clusterId" v:"required#Require clusterId."`
	Namespace string   `json:"namespace" v:"required#Require namespace."`
	Pods      []string `json:"pods" v:"required#Require pods."`
}

type ListPodMetricsRes struct {
	PodMetrics []*k8s2.PodNameAndMetrics `json:"podMetrics"`
}

type AnalyzePodReq struct {
	g.Meta    `path:"/resource/pod/analyze" tags:"pods" method:"get" summary:"analyze pod"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Name      string `json:"name" v:"required#Require name."`
}

type PodCrashLogsReq struct {
	g.Meta    `path:"/resource/pod/crash-logs/list" tags:"pods" method:"get" summary:"pod crash logs"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Pod       string `json:"pod" v:"required#Require pod."`
	Container string `json:"container" v:"required#Require container."`
	Line      int    `json:"line" d:"1000"`
}

type ListResourcePodRelatedContainerReq struct {
	g.Meta    `path:"/resource/pod/container/relate" tags:"pods" method:"get" summary:"list pod related pod"`
	ClusterId int    `json:"clusterId" v:"required#Require cluster."`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}

type ListContainerDirReq struct {
	g.Meta    `path:"/resource/pod/container/dir/list" tags:"pods" method:"get" summary:"list pod container dir"`
	ClusterId int    `json:"clusterId" v:"required#Require cluster."`
	Namespace string `json:"namespace"`
	Pod       string `json:"pod"`
	Container string `json:"container"`
	DirPath   string `json:"dirPath"`
}

type ListContainerFileReq struct {
	g.Meta    `path:"/resource/pod/container/file/list" tags:"pods" method:"get" summary:"list pod container file"`
	ClusterId int    `json:"clusterId" v:"required#Require cluster."`
	Namespace string `json:"namespace"`
	Pod       string `json:"pod"`
	Container string `json:"container"`
	DirPath   string `json:"dirPath"`
}

type ListRunningEphemeralContainerReq struct {
	g.Meta    `path:"/resource/pod/ephemeral-container/running/list" tags:"pods" method:"get" summary:"list pod of running ephemeral container"`
	ClusterId int    `json:"clusterId" v:"required#Require cluster."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Pod       string `json:"pod" v:"required#Require pod."`
}

type CreateEphemeralContainerReq struct {
	g.Meta          `path:"/resource/pod/ephemeral-container/create" tags:"pods" method:"post" summary:"create ephemeral container"`
	ClusterId       int    `json:"clusterId" v:"required#Require cluster."`
	Namespace       string `json:"namespace" v:"required#Require namespace."`
	Pod             string `json:"pod" v:"required#Require pod."`
	TargetContainer string `json:"targetContainer" v:"required#Require targetContainer."`
	ImageUrl        string `json:"imageUrl" v:"required#Require imageUrl."`
	DurationSec     int    `json:"durationSec" v:"required#Require durationSec."`
}

type ListPodLogSearchHistoryReq struct {
	g.Meta `path:"/resource/pod/log-search/history/list" tags:"pods" method:"get" summary:"list pod log search history"`
}

type SavePodLogSearchHistoryReq struct {
	g.Meta `path:"/resource/pod/log-search/history/save" tags:"pods" method:"post" summary:"save pod log search history"`
	Name   string `json:"name"`
	k8s2.PodLogSearchConditionV2
}

type DeletePodLogSearchHistoryReq struct {
	g.Meta `path:"/resource/pod/log-search/history/delete/:pid" tags:"pods" method:"delete" summary:"delete pod log search history"`
}

// ListPodRelatedSvcPortReq 获取pod关联svc的端口号
type ListPodRelatedSvcPortReq struct {
	g.Meta    `path:"/resource/pod/related/svc-port/list" tags:"pods" method:"get" summary:"list pod related svc port"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}
