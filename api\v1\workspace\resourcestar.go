package workspace

import "github.com/gogf/gf/v2/frame/g"

type ListCollectionClusterNsReq struct {
	g.Meta `path:"/workspace/collection/cluster-ns/list" tags:"Workspace" method:"get" summary:"get user collection cluster and namespace"`
}

type ListCollectionResourceReq struct {
	g.Meta `path:"/workspace/collection/resource/list" tags:"Workspace" method:"get" summary:"get user collection resource"`
	Search *string `json:"keyword"`
}

type GetCollectionResourceReq struct {
	g.Meta       `path:"/workspace/collection/resource/get" tags:"Workspace" method:"get" summary:"get user collection resource"`
	ClusterId    int    `json:"clusterId" v:"required#Require cluster id."`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
	ResourceName string `json:"resourceName" v:"required#Require resourceName."`
}

type CollectionResourceReq struct {
	g.Meta       `path:"/workspace/collection/resource/add" tags:"Workspace" method:"post" summary:"add user collection resource"`
	ClusterId    int    `json:"clusterId"  v:"required#Require cluster id."`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
	ResourceName string `json:"resourceName" v:"required#Require resourceName."`
	PageName     string `json:"pageName"`
}

type ClearCollectionResourceReq struct {
	g.Meta       `path:"/workspace/collection/resource/delete" tags:"Workspace" method:"delete" summary:"delete user collection resource"`
	CollectionId int `json:"collectionId" v:"required#Require collection id."`
}
