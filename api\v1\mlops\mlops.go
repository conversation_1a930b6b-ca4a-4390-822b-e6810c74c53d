package mlops

import (
	"github.com/gogf/gf/v2/frame/g"
	"k8s.io/apimachinery/pkg/api/resource"
)

type ListRayJobReq struct {
	g.Meta    `path:"/mlops/ray/job/list" method:"post" summary:"获取RayJob列表" tags:"mlops"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace string `json:"namespace" v:"required#Require namespace"`
	Page      int    `json:"page" d:"1"`
	Size      int    `json:"size" d:"10"`
	Search    string `json:"search" dc:"rayjob name for searching <PERSON><PERSON>ob"`
}

type CreateRayJobReq struct {
	g.Meta    `path:"/mlops/ray/job/create" method:"post" summary:"创建RayJob" tags:"mlops"`
	ClusterId int     `json:"clusterId" v:"required#Require clusterId"`
	Namespace string  `json:"namespace" v:"required#Require namespace"`
	JobName   string  `json:"jobName" v:"required#Require jobName"`
	ImageUrl  string  `json:"imageUrl" v:"required#Require imageUrl"`
	StartCmd  string  `json:"startCmd" v:"required#Require startCmd"`
	Worker    Worker  `json:"worker" v:"required#Require worker"`
	Mounts    []Mount `json:"mounts"`
}

type Mount struct {
	Pvc       string `json:"pvc" v:"required#Require pvc"`
	MountPath string `json:"mountPath" v:"required#Require mountPath"`
	SubPath   string `json:"subPath"`
}

type CreateRayJobWithYamlReq struct {
	g.Meta    `path:"/mlops/ray/job/yaml/create" method:"post" summary:"创建RayJob" tags:"mlops"`
	ClusterId int                    `json:"clusterId" v:"required#Require clusterId"`
	Namespace string                 `json:"namespace" v:"required#Require namespace"`
	Data      map[string]interface{} `json:"data" v:"required#Require data."`
}

type DeleteRayJobReq struct {
	g.Meta    `path:"/mlops/ray/job/delete" method:"delete" summary:"删除RayJob" tags:"mlops"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace string `json:"namespace" v:"required#Require namespace"`
	Name      string `json:"name" v:"required#Require namespace"`
}

type Worker struct {
	Replicas  int                   `json:"replicas" v:"required#Require replicas"`
	Resources *ResourceRequirements `json:"resources" v:"required#Require resources"`
}

type ResourceRequirements struct {
	Requests map[string]resource.Quantity `json:"requests"`
	Limits   map[string]resource.Quantity `json:"limits"`
}

type ListRayJobRelatedBriefStatisticsReq struct {
	g.Meta    `path:"/mlops/ray/job/related/brief-statistics/list" method:"get" summary:"获取RayJob关联的资源概览" tags:"mlops"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace string `json:"namespace" v:"required#Require namespace"`
	Name      string `json:"name" v:"required#Require namespace"`
}

type ListRayJobRelatedPodReq struct {
	g.Meta    `path:"/mlops/ray/job/related/pod/list" method:"get" summary:"获取RayJob关联的Pod列表" tags:"mlops"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace string `json:"namespace" v:"required#Require namespace"`
	Name      string `json:"name" v:"required#Require namespace"`
}

type ListRayJobRelatedServiceReq struct {
	g.Meta    `path:"/mlops/ray/job/related/service/list" method:"get" summary:"获取RayJob关联的Service列表" tags:"mlops"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace string `json:"namespace" v:"required#Require namespace"`
	Name      string `json:"name" v:"required#Require namespace"`
}

type ListRayJobRelatedConfigmapReq struct {
	g.Meta    `path:"/mlops/ray/job/related/configmap/list" method:"get" summary:"获取RayJob关联的Configmap列表" tags:"mlops"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace string `json:"namespace" v:"required#Require namespace"`
	Name      string `json:"name" v:"required#Require namespace"`
}

type ListRayJobRelatedSecretReq struct {
	g.Meta    `path:"/mlops/ray/job/related/secret/list" method:"get" summary:"获取RayJob关联的Secret列表" tags:"mlops"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace string `json:"namespace" v:"required#Require namespace"`
	Name      string `json:"name" v:"required#Require namespace"`
}

type ListRayJobRelatedPvcReq struct {
	g.Meta    `path:"/mlops/ray/job/related/pvc/list" method:"get" summary:"获取RayJob关联的Pvc列表" tags:"mlops"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace string `json:"namespace" v:"required#Require namespace"`
	Name      string `json:"name" v:"required#Require namespace"`
}
