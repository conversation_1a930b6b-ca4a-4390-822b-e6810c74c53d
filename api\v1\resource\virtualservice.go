package resource

import "github.com/gogf/gf/v2/frame/g"

// ----------------------------------------------- virtualService-----------------------------------------------
type ListResourceVirtualServiceReq struct {
	g.Meta    `path:"/resource/virtualservice/list" method:"get" tags:"Virtual Service" sm:"" auth:"k8s-namespaceMeshVS-r"`
	ClusterID int    `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Page      int    `json:"page" d:"1"`
	Size      int    `json:"size" d:"10"`
	Search    string `json:"search" dc:"vs name for searching virtual service"`
}

type ListDelagateVirtualServiceReq struct {
	g.Meta    `path:"/resource/virtualservice/delegate/list" method:"get" tags:"Virtual Service" sm:"list delegate virtual service" auth:"k8s-namespaceMeshVS-r"`
	ClusterID int    `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace string `json:"namespace" v:"required#Require namespace."`
}

type GetVirtualServiceDetailsReq struct {
	g.Meta       `path:"/resource/virtualservice/get" method:"get" tags:"Virtual Service" sm:"virtual service details" auth:"k8s-namespaceMeshVS-r"`
	ResourceName string `json:"resource_name" v:"required#Require resourcename." dc:"vs name"`
	ClusterID    int    `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
}

type DeleteVirtualServiceReq struct {
	g.Meta       `path:"/resource/virtualservice/delete" method:"delete" tags:"Virtual Service" sm:"delete virtual service" auth:"k8s-namespaceMeshVS-r"`
	ResourceName string `json:"resource_name" v:"required#Require resourcename." dc:"vs name"`
	ClusterID    int    `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
}

type GetRelatedGatewaysReq struct {
	g.Meta    `path:"/resource/related/virtualservice/gateway/list" method:"get" tags:"Virtual Service" sm:"gateways raleted virtual service's hosts" auth:"k8s-namespaceMeshVS-r"`
	Hosts     []string `json:"hosts"  dc:"host list in virtual service"`
	ClusterID int      `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace string   `json:"namespace" v:"required#Require namespace."`
}

type GetRelatedDestinationRulesReq struct {
	g.Meta    `path:"/resource/related/virtualservice/destinationrule/list" method:"get" tags:"Virtual Service" sm:"destinationrules raleted virtual service" auth:"k8s-namespaceMeshVS-r"`
	Host      string `json:"host"  dc:"destination.host in virtual service"`
	ClusterID int    `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace string `json:"namespace" v:"required#Require namespace."`
}

type ListRelatedVsServiceReq struct {
	g.Meta    `path:"/resource/related/virtualservice/service/list" method:"get" tags:"Virtual Service" sm:"vs raleted virtual service service" auth:"k8s-namespaceMeshVS-r"`
	ClusterID int    `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace string `json:"namespace"`
}

type ListVirtualServiceHistoryReq struct {
	g.Meta       `path:"/resource/virtualservice/history/list" method:"get" tags:"Virtual Service" sm:"virtual service history" auth:"k8s-namespaceMeshVS-r"`
	ResourceName string `json:"resource_name" v:"required#Require resourcename." dc:"vs name"`
	ClusterID    int    `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
}

type CreateVirtualServiceReq struct {
	g.Meta     `path:"/resource/virtualservice/create" method:"post" tags:"Virtual Service" auth:"k8s-namespaceMeshVS-w"`
	Data       interface{} `json:"data" v:"required#Require test." dc:"text for k8s"`
	ClusterID  int         `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace  string      `json:"namespace" v:"required#Require namespace."`
	Annotation string      `json:"annotation" v:"required#Require annotation."`
}

type UpdateVirtualServiceReq struct {
	g.Meta     `path:"/resource/virtualservice/update" method:"post" tags:"Virtual Service" auth:"k8s-namespaceMeshVS-w"`
	Data       interface{} `json:"data" v:"required#Require test." dc:"text for k8s"`
	ClusterID  int         `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace  string      `json:"namespace" v:"required#Require namespace."`
	Annotation string      `json:"annotation" v:"required#Require annotation."`
}

type CreateCopyVirtualServiceReq struct {
	g.Meta          `path:"/resource/virtualservice/copy/create" method:"post" tags:"Virtual Service" auth:"k8s-namespaceMeshVS-w"`
	NewClusterID    int    `json:"new_cluster_id" v:"required#Require cluster." dc:"cluster id"`
	NewNamespace    string `json:"new_namespace" v:"required#Require namespace."`
	OriginClusterID int    `json:"origin_cluster_id" v:"required#Require cluster." dc:"cluster id"`
	OriginNamespace string `json:"origin_namespace" v:"required#Require namespace."`
	ResourceName    string `json:"resource_name" v:"required#Require resourcename." dc:"vs name"`
}

type CreateVirtualServiceCheckReq struct {
	g.Meta     `path:"/resource/virtualservice/create/check" method:"post" tags:"Virtual Service" auth:"k8s-namespaceMeshVS-w"`
	Data       interface{} `json:"data" v:"required#Require test." dc:"text for k8s"`
	ClusterID  int         `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace  string      `json:"namespace" v:"required#Require namespace."`
	Annotation string      `json:"annotation" v:"required#Require annotation."`
}

type UpdateVirtualServiceCheckReq struct {
	g.Meta     `path:"/resource/virtualservice/update/check" method:"post" tags:"Virtual Service" auth:"k8s-namespaceMeshVS-w"`
	Data       interface{} `json:"data" v:"required#Require test." dc:"text for k8s"`
	ClusterID  int         `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace  string      `json:"namespace" v:"required#Require namespace."`
	Annotation string      `json:"annotation" v:"required#Require annotation."`
}
