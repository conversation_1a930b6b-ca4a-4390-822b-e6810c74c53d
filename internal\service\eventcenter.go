package service

import "tt-cloud-enterprise/internal/model/dto"

type IEventCenter interface {
	ExtractEvent(user *dto.ContextUser, path, mothod string, body []byte)
}

var localEventCenter IEventCenter

func EventCenter() IEventCenter {
	if localEventCenter == nil {
		panic("implement not found for interface IEventCenter, forgot register?")
	}
	return localEventCenter
}

func RegisterEventCenter(i IEventCenter) {
	localEventCenter = i
}
