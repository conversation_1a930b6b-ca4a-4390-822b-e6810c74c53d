package resource

import "github.com/gogf/gf/v2/frame/g"

type ListResourceJobReq struct {
	g.Meta    `path:"/resource/job/list" tags:"job" method:"get" summary:"list job"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Search    string `json:"search"`
	Page      int    `json:"page" d:"1"`
	Size      int    `json:"size" d:"10"`
}

type GetResourceJobDetailReq struct {
	g.Meta    `path:"/resource/job/detail" tags:"job" method:"get" summary:"get job detail"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Name      string `json:"name" v:"required#Require name."`
}

type ListResourceJobRelatedPodReq struct {
	g.Meta    `path:"/resource/job/related/pod" tags:"job" method:"get" summary:"list job related pod"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Name      string `json:"name" v:"required#Require name."`
}

type DeleteResourceJobReq struct {
	g.Meta    `path:"/resource/job/delete" tags:"job" method:"delete" summary:"delete job" auth:"k8s-namespaceJob-w"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace"`
	Name      string `json:"name" v:"required#Require name."`
}

type CreateResourceJobYamlReq struct {
	g.Meta    `path:"/resource/job/yaml/create" tags:"job" method:"post" summary:"create job object with yaml" auth:"k8s-namespaceJob-w"`
	ClusterId int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                 `json:"namespace"`
	Data      map[string]interface{} `json:"data" v:"required#Require name."`
}

type UpdateResourceJobYamlReq struct {
	g.Meta    `path:"/resource/job/yaml/update" tags:"job" method:"post" summary:"update job object with yaml" auth:"k8s-namespaceJob-w"`
	ClusterId int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                 `json:"namespace"`
	Data      map[string]interface{} `json:"data" v:"required#Require name."`
}
