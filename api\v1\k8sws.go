package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"tt-cloud-enterprise/internal/model/dto/k8s"
)

type PodLogsWebSocketReq struct {
	g.Meta    `path:"/ws/resource/pod/logs" tags:"Pods" method:"get" summary:"pod logs websocket" auth:"k8s-namespaceWorkload-r"`
	ClusterId int    `json:"cluster_id"`
	Namespace string `json:"namespace"`
	Pod       string `json:"pod"`
	Container string `json:"container"`
	Tail      int    `json:"tail"`
}

type PodContainerWebSocketReq struct {
	g.Meta    `path:"/ws/resource/pod/container/exec" tags:"Pods" method:"get" summary:"pod container exec" auth:"k8s-namespaceWorkload-r"`
	ClusterId int    `json:"cluster_id"`
	Namespace string `json:"namespace"`
	Pod       string `json:"pod"`
	Container string `json:"container"`
	Exec      string `json:"exec"`
	Priority  bool   `json:"priority"`
}

type WebsocketRes struct {
	g.Meta `mime:"text/html" type:"string" example:"<html/>" dc:"It redirects to homepage if success"`
}

type PodLogSearchWebSocketReq struct {
	g.Meta    `path:"/ws/resource/pod/log/search" tags:"Pods" method:"get" summary:"pod log websocket" auth:"k8s-namespaceWorkload-r"`
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Pod       string `json:"pod"`
	Container string `json:"container"`
	k8s.PodLogCondition
}

type PodLogRes struct {
	g.Meta `mime:"text/html" type:"string" example:"<html/>" dc:"It redirects to homepage if success"`
}

type WorkloadLogsWebSocketReq struct {
	g.Meta       `path:"/ws/resource/workload/logs" tags:"ws" method:"get" summary:"workload logs websocket" auth:"k8s-namespaceWorkload-r"`
	ClusterId    int    `json:"clusterId"`
	Namespace    string `json:"namespace"`
	WorkloadKind string `json:"workloadKind"`
	WorkloadName string `json:"workloadName"`
	Container    string `json:"containerName"`
	Tail         int    `json:"tail"`
}

type SearchWorkloadLogsWebSocketReq struct {
	g.Meta       `path:"/ws/resource/workload/log/search" tags:"ws" method:"get" summary:"search workload logs websocket" auth:"k8s-namespaceWorkload-r"`
	ClusterId    int    `json:"clusterId"`
	Namespace    string `json:"namespace"`
	WorkloadKind string `json:"workloadKind"`
	WorkloadName string `json:"workloadName"`
	Container    string `json:"containerName"`
	Line         int64  `json:"line"`
	k8s.PodLogSearchConditionV2
}
