package v1

import "github.com/gogf/gf/v2/frame/g"

// 分页setting
type ListSettingReq struct {
	g.<PERSON>a `path:"/setting/list" method:"get" tags:"Setting" sm:"list setting"`
	ListReq
}

// 根据pid查setting
type GetSettingReq struct {
	g.Meta `path:"/setting/:pid" method:"get" tags:"Setting" sm:"get setting"`
}

type CreateSettingReq struct {
	g.Meta   `path:"/setting/create" method:"post" tags:"Setting" sm:"create setting"`
	Key      string `json:"key"`
	Value    string `json:"value"`
	Desc     string `json:"desc"`
	Category string `json:"category"`
}

type UpdateSettingReq struct {
	g.Meta   `path:"/setting/update/:pid" method:"patch" tags:"Setting" sm:"update setting"`
	Value    string `json:"value"`
	Desc     string `json:"desc"`
	Category string `json:"category"`
}

type DeleteSettingReq struct {
	g.Meta `path:"/setting/delete/:pid" method:"delete" tags:"Setting" sm:"delete setting"`
}

type ListNotifySettingReq struct {
	g.Meta `path:"/resource-notify-policy/setting/list" method:"get" tags:"NotifySetting" sm:"list notify setting"`
	ListReq
}

type CreateNotifySettingReq struct {
	g.Meta            `path:"/resource-notify-policy/setting/create" method:"post" tags:"NotifySetting" sm:"create notify setting"`
	Kind              string `json:"kind" v:"required#Require kind."`
	SendInterval      int    `json:"sendInterval" v:"required#Require sendInterval."`
	SameEventInterval int    `json:"sameEventInterval" v:"required#Require sameEventInterval."`
}

type DeleteNotifySettingReq struct {
	g.Meta `path:"/resource-notify-policy/setting/delete" method:"delete" tags:"NotifySetting" sm:"delete notify setting"`
	Pid    int `json:"pid" v:"required#Require id."`
}

type UpdateNotifySettingReq struct {
	g.Meta            `path:"/resource-notify-policy/setting/update" method:"post" tags:"NotifySetting" sm:"update notify setting"`
	Pid               int    `json:"pid" v:"required#Require id."`
	Kind              string `json:"kind" v:"required#Require kind."`
	SendInterval      int    `json:"sendInterval" v:"required#Require sendInterval."`
	SameEventInterval int    `json:"sameEventInterval" v:"required#Require sameEventInterval."`
}
