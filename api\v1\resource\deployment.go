package resource

import (
	"github.com/gogf/gf/v2/frame/g"

	"tt-cloud-enterprise/internal/model/dto/istio"
	k8s2 "tt-cloud-enterprise/internal/model/dto/k8s"
	"tt-cloud-enterprise/tools/k8s"
)

// ------------------------------------------------deployment-----------------------------------
type ListResourceDeploymentReq struct {
	g.Meta    `path:"/resource/deployment/list" tags:"deployments" method:"post" summary:"list resource deployment" auth:"k8s-namespaceWorkload-r"`
	ClusterID int    `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace string `json:"namespace" v:"required#Require namespace." dc:"namespace"`
	Search    string `json:"search"  dc:"search"`
	Page      int    `json:"page" d:"1"`
	Size      int    `json:"size" d:"10"`
	// LabelSelectorMap map[string]k8s.LabelSelector `json:"label_selector_map"`
	LabelSelector []k8s.LabelSelector `json:"label_selector" dc:"labelSelector"`
}

type GetResourceDeploymentRelatedReplicaSetReq struct {
	g.Meta    `path:"/resource/deployment/related/rs" tags:"deployments" method:"get" summary:"get resource related deployment rs "`
	Uuid      string `json:"uuid" `
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}

type GetResourceDeploymentRelatedReplicaSetPodReq struct {
	g.Meta     `path:"/resource/deployment/related/rs/pod" tags:"deployments" method:"get" summary:"get resource related deployment rs pod"`
	RsUuid     string `json:"rs_uuid"`
	DeployUuid string `json:"deploy_uuid"`
	ClusterId  int    `json:"clusterId"`
	Namespace  string `json:"namespace"`
	RsName     string `json:"rsName"`
}

type GetResourceDeploymentRelatedServiceReq struct {
	g.Meta    `path:"/resource/deployment/related/service" tags:"deployments" method:"get" summary:"get resource related deployment rs "`
	Uuid      string `json:"uuid" `
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}

type GetResourceDeploymentRelatedServiceFQDNReq struct {
	g.Meta    `path:"/resource/deployment/related/service/fqdn" tags:"deployments" method:"get" summary:"get resource related deployment rs "`
	Uuid      string `json:"uuid" `
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}

// -----------------configmap----------------

type GetResourceDeploymentRelatedConfigmapReq struct {
	g.Meta    `path:"/resource/deployment/related/configmap" tags:"deployments" method:"get" summary:"get resource related deployment service "`
	Uuid      string `json:"uuid" `
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}

type GetResourceDeploymentRelatedConfigmapDetailReq struct {
	g.Meta    `path:"/resource/deployment/related/configmap/get" tags:"deployments" method:"get" summary:"get resource:deployment related configmap "`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Name      string `json:"name" v:"required#Require name."`
}

type ListResourceDeploymentRelatedConfigmapReq struct {
	g.Meta       `path:"/resource/deployment/related/configmap/list" tags:"deployments" method:"get" summary:"list resource:deployment related configmap "`
	ClusterId    int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
	WorkloadName string `json:"workloadName" v:"required#Require workloadName."`
}

// ------------------hpa------------------

type GetResourceDeploymentRelatedHpaReq struct {
	g.Meta    `path:"/resource/deployment/related/hpa" tags:"deployments" method:"get" summary:"get resource related deployment hpa "`
	Uuid      string `json:"uuid" `
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}

type GetResourceDeploymentRelatedHPADetailReq struct {
	g.Meta    `path:"/resource/deployment/related/hpa/get" tags:"deployments" method:"get" summary:"get resource:deployment related hpa "`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Name      string `json:"name" v:"required#Require name."`
}

type ListResourceDeploymentRelatedHPAReq struct {
	g.Meta       `path:"/resource/deployment/related/hpa/list" tags:"deployments" method:"get" summary:"list resource:deployment related hpa "`
	ClusterId    int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
	WorkloadName string `json:"workloadName" v:"required#Require workloadName."`
}

type GetResourceDeploymentRelatedHPAFormReq struct {
	g.Meta    `path:"/resource/deployment/related/hpa/form/get" tags:"deployments" method:"get" summary:"get resource:deployment related hpa form "`
	ClusterId int          `json:"clusterId" v:"required#Require clusterId."`
	Namespace string       `json:"namespace" v:"required#Require namespace."`
	Name      string       `json:"name" v:"required#Require name."`
	Kind      k8s2.HpaKind `json:"kind" v:"required#Require kind."`
}

type CreateResourceDeploymentRelatedHPAFormReq struct {
	g.Meta    `path:"/resource/deployment/related/hpa/form/create" tags:"deployments" method:"post" summary:"create resource:deployment related hpa form " auth:"k8s-namespaceWorkload-w"`
	ClusterId int          `json:"clusterId" v:"required#Require clusterId."`
	Namespace string       `json:"namespace" v:"required#Require namespace."`
	HpaForm   k8s2.HpaForm `json:"hpaForm" v:"required#Require hpaForm."`
}

type UpdateResourceDeploymentRelatedHPAFormReq struct {
	g.Meta    `path:"/resource/deployment/related/hpa/form/update" tags:"deployments" method:"post" summary:"update resource:deployment related  hpa form " auth:"k8s-namespaceWorkload-w"`
	ClusterId int          `json:"clusterId" v:"required#Require clusterId."`
	Namespace string       `json:"namespace" v:"required#Require namespace."`
	Kind      k8s2.HpaKind `json:"kind" v:"required#Require kind."`
	HpaForm   k8s2.HpaForm `json:"hpaForm" v:"required#Require hpaForm."`
}

type DeleteResourceDeploymentRelatedHPAReq struct {
	g.Meta    `path:"/resource/deployment/related/hpa/delete" tags:"deployments" method:"delete" summary:"delete resource:deployment related hpa" auth:"k8s-namespaceWorkload-w"`
	ClusterId int          `json:"clusterId" v:"required#Require clusterId."`
	Namespace string       `json:"namespace" v:"required#Require namespace."`
	Name      string       `json:"name" v:"required#Require name."`
	Kind      k8s2.HpaKind `json:"kind" v:"required#Require kind."`
}

type CheckResourceDeploymentRelatedHpaIsConvertReq struct {
	g.Meta    `path:"/resource/deployment/related/hpa/form/is-convert" tags:"hpa" method:"get" summary:"check hpa convert" `
	ClusterId int          `json:"clusterId" v:"required#Require clusterId."`
	Namespace string       `json:"namespace" v:"required#Require namespace."`
	Name      string       `json:"name" v:"required#Require name."`
	Kind      k8s2.HpaKind `json:"kind" v:"required#Require kind."`
}

// CheckResourceDeploymentRelatedHpaSupportKedaReq 在当前集群对hpa开启定时伸缩
type CheckResourceDeploymentRelatedHpaSupportKedaReq struct {
	g.Meta    `path:"/resource/deployment/related/hpa/keda/is-support" tags:"hpa" method:"get" summary:"check support keda" `
	ClusterId int `json:"clusterId" v:"required#Require clusterId."`
}

// ListResourceDeploymentRelatedTimeZoneReq 时区列表
type ListResourceDeploymentRelatedTimeZoneReq struct {
	g.Meta `path:"/resource/deployment/related/hpa/timezone/list" tags:"hpa" method:"get" summary:"list timezone" `
}

type CreateResourceDeploymentRelatedHpaReq struct {
	g.Meta    `path:"/resource/deployment/related/hpa/yaml/create" tags:"hpa" method:"post" summary:"create hpa" auth:"k8s-namespaceWorkload-w" `
	ClusterId int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                 `json:"namespace" v:"required#Require namespace."`
	Data      map[string]interface{} `json:"data" v:"required#Require data."`
}

type UpdateResourceDeploymentRelatedHpaReq struct {
	g.Meta    `path:"/resource/deployment/related/hpa/yaml/update" tags:"hpa" method:"post" summary:"update hpa" auth:"k8s-namespaceWorkload-w" `
	ClusterId int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                 `json:"namespace" v:"required#Require namespace."`
	Data      map[string]interface{} `json:"data" v:"required#Require data."`
}

type ListResourceDeploymentRelatedBriefStatisticsReq struct {
	g.Meta       `path:"/resource/deployment/related/brief-statistics/list" tags:"hpa" method:"get" summary:"update hpa"`
	ClusterId    int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
	WorkloadName string `json:"workloadName" v:"required#Require workloadName."`
}

type ListResourceDeploymentRelatedBriefStatisticsRes struct {
	k8s2.DeploymentRelatedResourceStatisticsCount
}

// ------------------secret------------------

type GetResourceDeploymentRelatedSecretReq struct {
	g.Meta    `path:"/resource/deployment/related/secret" tags:"deployments" method:"get" summary:"get resource related deployment secret "`
	Uuid      string `json:"uuid" `
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}

type GetResourceDeploymentRelatedSecretDetailReq struct {
	g.Meta    `path:"/resource/deployment/related/secret/get" tags:"deployments" method:"get" summary:"get resource:deployment related secret "`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Name      string `json:"name" v:"required#Require name."`
}

type ListResourceDeploymentRelatedSecretReq struct {
	g.Meta       `path:"/resource/deployment/related/secret/list" tags:"deployments" method:"get" summary:"list resource:deployment related secret "`
	ClusterId    int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
	WorkloadName string `json:"workloadName" v:"required#Require workloadName."`
}

type GetResourceDeploymentRelatedPvcReq struct {
	g.Meta    `path:"/resource/deployment/related/pvc" tags:"deployments" method:"get" summary:"get resource related deployment pvc "`
	Uuid      string `json:"uuid" `
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}

type GetDeploymentResourceJsonReq struct {
	g.Meta       `path:"/resource/deployment/json/get" tags:"deployments" method:"get" summary:"deployment resource get json "`
	Kind         string `json:"kind"`
	ClusterId    int    `json:"cluster_id"`
	Namespace    string `json:"namespace"`
	ResourceName string `json:"resource_name"`
}

type UpdateConfigmapReq struct {
	g.Meta     `path:"/resource/deployment/configmap/update" tags:"deployments" method:"post" summary:"configmap update" auth:"k8s-namespaceWorkload-w"`
	ClusterId  int    `json:"cluster_id"`
	Namespace  string `json:"namespace"`
	Deployment string `json:"deployment"`
	Yaml       string `json:"yaml"`
}

type UpdateHPAReq struct {
	g.Meta     `path:"/resource/deployment/hpa/update" tags:"deployments" method:"post" summary:"hpa update" auth:"k8s-namespaceWorkload-w"`
	ClusterId  int    `json:"cluster_id"`
	Namespace  string `json:"namespace"`
	Deployment string `json:"deployment"`
	Yaml       string `json:"yaml"`
}

type DeleteDeploymentRelatedPodReq struct {
	g.Meta    `path:"/resource/deployment/related/pod/delete" tags:"deployments" method:"delete" summary:"delete pod" auth:"k8s-namespaceWorkload-w"`
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}

// ------------------destinationrule------------------

type ListResourceDeploymentRelatedDestinationRuleReq struct {
	g.Meta    `path:"/resource/deployment/related/destinationrule/list" tags:"deployments" method:"get" summary:"get resource related deployment destinationrule"`
	UUid      string `json:"uuid" `
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}

type GetDeploymentRelatedDestinationRuleDetailReq struct {
	g.Meta       `path:"/resource/deployment/related/destinationrule/get" method:"get" tags:"DestinationRule" sm:"DestinationRule details" auth:"k8s-namespaceMeshDR-r"`
	ResourceName string `json:"resourceName" v:"required#Require resourcename." dc:"se name"`
	ClusterID    int    `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
}

type DeleteDeploymentRelatedDestinationRuleV2Req struct {
	g.Meta       `path:"/resource/deployment/related/destinationrule/delete" method:"delete" tags:"DestinationRule" sm:"delete DestinationRule" auth:"k8s-namespaceMeshDR-w"`
	ResourceName string `json:"resourceName" v:"required#Require resourcename." dc:"se name"`
	ClusterID    int    `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
}

type GetDeploymentRelatedDestinationRuleIsConvertToFormReq struct {
	g.Meta       `path:"/resource/deployment/related/destinationrule/form/is-convert" method:"get" tags:"DestinationRule" sm:"form-convert DestinationRule" auth:"k8s-namespaceMeshDR-r"`
	ResourceName string `json:"resourceName" v:"required#Require resourcename." dc:"se name"`
	ClusterID    int    `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
}

type CreateDeploymentRelatedDestinationRuleByFormReq struct {
	g.Meta                  `path:"/resource/deployment/related/destinationrule/form/create" method:"post" tags:"DestinationRule" sm:"form-create DestinationRule" auth:"k8s-namespaceMeshDR-w"`
	ClusterID               int                       `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace               string                    `json:"namespace" v:"required#Require namespace."`
	IsApplyToUnifiedCluster bool                      `json:"isApplyToUnifiedCluster" v:"required#Require IsApplyToUnifiedCluster."`
	Data                    istio.DestinationRuleForm `json:"data" v:"required#Require data."`
}

type GetDeploymentRelatedDestinationRuleFormReq struct {
	g.Meta       `path:"/resource/deployment/related/destinationrule/form/get" method:"get" tags:"DestinationRule" sm:"DestinationRule form" auth:"k8s-namespaceMeshDR-r"`
	ResourceName string `json:"resourceName" v:"required#Require resourcename." dc:"se name"`
	ClusterID    int    `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
}

type UpdateDeploymentRelatedDestinationRuleByFormReq struct {
	g.Meta                  `path:"/resource/deployment/related/destinationrule/form/update" method:"post" tags:"DestinationRule" sm:"form-update DestinationRule" auth:"k8s-namespaceMeshDR-w"`
	ResourceName            string `json:"resourceName" v:"required#Require resourcename." dc:"se name"`
	ClusterID               int    `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace               string `json:"namespace" v:"required#Require namespace."`
	IsApplyToUnifiedCluster bool   `json:"isApplyToUnifiedCluster" v:"required#Require IsApplyToUnifiedCluster."`

	Data istio.DestinationRuleForm `json:"data" v:"required#Require data."`
}

type CreateDeploymentRelatedDestinationRuleByYamlReq struct {
	g.Meta                  `path:"/resource/deployment/related/destinationrule/yaml/create" tags:"DestinationRule" method:"post" summary:"create DestinationRule by yaml" auth:"k8s-namespaceMeshDR-w"`
	ClusterID               int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace               string `json:"namespace" v:"required#Require namespace."`
	IsApplyToUnifiedCluster bool   `json:"isApplyToUnifiedCluster" v:"required#Require IsApplyToUnifiedCluster."`

	Data map[string]interface{} `json:"data" v:"required#Require data."`
}

type UpdateDeploymentRelatedDestinationRuleByYamlReq struct {
	g.Meta                  `path:"/resource/deployment/related/destinationrule/yaml/update" tags:"DestinationRule" method:"post" summary:"update DestinationRule by yaml" auth:"k8s-namespaceMeshDR-w"`
	ClusterID               int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace               string                 `json:"namespace" v:"required#Require namespace."`
	IsApplyToUnifiedCluster bool                   `json:"isApplyToUnifiedCluster" v:"required#Require IsApplyToUnifiedCluster."`
	Data                    map[string]interface{} `json:"data" v:"required#Require data."`
}

type CheckDeploymentRelatedDestinationRuleByFormReq struct {
	g.Meta    `path:"/resource/deployment/related/destinationrule/form/check" tags:"DestinationRule" method:"post" summary:"check DestinationRule by form" auth:"k8s-namespaceMeshDR-r"`
	ClusterID int                       `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                    `json:"namespace" v:"required#Require namespace."`
	Data      istio.DestinationRuleForm `json:"data" v:"required#Require data."`
	Action    string                    `json:"action" v:"required#Require namespace.|in:create,update"`
}

type CheckDeploymentRelatedDestinationRuleByYamlReq struct {
	g.Meta    `path:"/resource/deployment/related/destinationrule/yaml/check" tags:"DestinationRule" method:"post" summary:"check DestinationRule by yaml" auth:"k8s-namespaceMeshDR-r"`
	ClusterID int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                 `json:"namespace" v:"required#Require namespace."`
	Data      map[string]interface{} `json:"data" v:"required#Require data."`
	Action    string                 `json:"action" v:"required#Require namespace.|in:create,update"`
}

type CreateDeploymentRelatedCopyDestinationRuleV2Req struct {
	g.Meta          `path:"/resource/deployment/related/destinationrule/copy/create" method:"post" tags:"destinationrule" auth:"k8s-namespaceMeshDR-w"`
	NewClusterID    int    `json:"newClusterId" v:"required#Require cluster." dc:"cluster id"`
	NewNamespace    string `json:"newNamespace" v:"required#Require namespace."`
	OriginClusterID int    `json:"originClusterId" v:"required#Require cluster." dc:"cluster id"`
	OriginNamespace string `json:"originNamespace" v:"required#Require namespace."`
	ResourceName    string `json:"resourceName" v:"required#Require resourcename." dc:"dr name"`
}

type ListDeploymentRelatedDestinationRuleServiceReq struct {
	g.Meta    `path:"/resource/deployment/related/destinationrule/service/list" method:"get" tags:"DestinationRule" sm:"" auth:"k8s-namespaceMeshDR-r"`
	UUid      string `json:"uuid" `
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}

type GetDeploymentRelatedDestinationRuleRelatedPodsLabelReq struct {
	g.Meta    `path:"/resource/deployment/related/destinationrule/related/pod/label/list" method:"get" tags:"DestinationRule" sm:"" auth:"k8s-namespaceMeshDR-r"`
	ClusterID int    `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Host      string `json:"host" v:"required#Require host."`
}

type PreviewDeploymentRelatedDestinationRuleByFormReq struct {
	g.Meta    `path:"/resource/deployment/related/destinationrule/form/preview" method:"post" tags:"DestinationRule" sm:"form-preview DestinationRule" auth:"k8s-namespaceMeshDR-r"`
	ClusterID int                       `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace string                    `json:"namespace" v:"required#Require namespace."`
	Data      istio.DestinationRuleForm `json:"data" v:"required#Require data."`
	Action    string                    `json:"action" v:"required#Require namespace.|in:create,update"`
}

// SetDeploymentRelatedPodSidecarLogLevelReq 设置 sidecar容器istio-proxy的日志采集级别 , debug
type SetDeploymentRelatedPodSidecarLogLevelReq struct {
	g.Meta    `path:"/resource/deployment/related/pod/sidecar/log-level/set" tags:"deployments" method:"post" summary:"set deployment related pod sidecar log level set"`
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Pod       string `json:"pod"`
	Level     string `json:"level"`
}

// ------------------virtualservice------------------

type ListDeploymentRelatedVirtualServiceReq struct {
	g.Meta    `path:"/resource/deployment/related/virtualservice/list" method:"get" tags:"Deployment" sm:"" auth:"k8s-namespaceMeshVS-r"`
	UUid      string `json:"uuid" `
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}

type GetDeploymentRelatedVirtualServiceDetailReq struct {
	g.Meta       `path:"/resource/deployment/related/virtualservice/get" method:"get" tags:"Deployment" sm:"VirtualService details" auth:"k8s-namespaceMeshVS-r"`
	ResourceName string `json:"resourceName" v:"required#Require resourcename." dc:"vs name"`
	ClusterID    int    `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
}

type DeleteDeploymentRelatedVirtualServiceReq struct {
	g.Meta       `path:"/resource/deployment/related/virtualservice/delete" method:"delete" tags:"Deployment" sm:"delete VirtualService" auth:"k8s-namespaceMeshVS-w"`
	ResourceName string `json:"resourceName" v:"required#Require resourcename." dc:"vs name"`
	ClusterID    int    `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
}

type GetDeploymentRelatedVirtualServiceIsConvertToFormReq struct {
	g.Meta       `path:"/resource/deployment/related/virtualservice/form/is-convert" method:"get" tags:"Deployment" sm:"form-convert VirtualService" auth:"k8s-namespaceMeshVS-r"`
	ResourceName string `json:"resourceName" v:"required#Require resourcename." dc:"vs name"`
	ClusterID    int    `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
}

type CreateDeploymentRelatedVirtualServiceByFormReq struct {
	g.Meta                  `path:"/resource/deployment/related/virtualservice/form/create" method:"post" tags:"Deployment" sm:"form-create VirtualService" auth:"k8s-namespaceMeshVS-w"`
	ClusterID               int                      `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace               string                   `json:"namespace" v:"required#Require namespace."`
	IsApplyToUnifiedCluster bool                     `json:"isApplyToUnifiedCluster" v:"required#Require IsApplyToUnifiedCluster."`
	Data                    istio.VirtualServiceForm `json:"data" v:"required#Require data."`
}

type GetDeploymentRelatedVirtualServiceFormReq struct {
	g.Meta       `path:"/resource/deployment/related/virtualservice/form/get" method:"get" tags:"Deployment" sm:"VirtualService form" auth:"k8s-namespaceMeshVS-r"`
	ResourceName string `json:"resourceName" v:"required#Require resourcename." dc:"vs name"`
	ClusterID    int    `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
}

type UpdateDeploymentRelatedVirtualServiceByFormReq struct {
	g.Meta                  `path:"/resource/deployment/related/virtualservice/form/update" method:"post" tags:"Deployment" sm:"form-update VirtualService" auth:"k8s-namespaceMeshVS-w"`
	ResourceName            string                   `json:"resourceName" v:"required#Require resourcename." dc:"vs name"`
	ClusterID               int                      `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace               string                   `json:"namespace" v:"required#Require namespace."`
	IsApplyToUnifiedCluster bool                     `json:"isApplyToUnifiedCluster" v:"required#Require IsApplyToUnifiedCluster."`
	Data                    istio.VirtualServiceForm `json:"data" v:"required#Require data."`
}

type CreateDeploymentRelatedVirtualServiceByYamlReq struct {
	g.Meta                  `path:"/resource/deployment/related/virtualservice/yaml/create" tags:"Deployment" method:"post" summary:"create VirtualService by yaml" auth:"k8s-namespaceMeshVS-w"`
	ClusterID               int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace               string                 `json:"namespace" v:"required#Require namespace."`
	IsApplyToUnifiedCluster bool                   `json:"isApplyToUnifiedCluster" v:"required#Require IsApplyToUnifiedCluster."`
	Data                    map[string]interface{} `json:"data" v:"required#Require data."`
}

type UpdateDeploymentRelatedVirtualServiceByYamlReq struct {
	g.Meta                  `path:"/resource/deployment/related/virtualservice/yaml/update" tags:"Deployment" method:"post" summary:"update VirtualService by yaml" auth:"k8s-namespaceMeshVS-w"`
	ClusterID               int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace               string                 `json:"namespace" v:"required#Require namespace."`
	IsApplyToUnifiedCluster bool                   `json:"isApplyToUnifiedCluster" v:"required#Require IsApplyToUnifiedCluster."`
	Data                    map[string]interface{} `json:"data" v:"required#Require data."`
}

type CheckDeploymentRelatedVirtualServiceByFormReq struct {
	g.Meta    `path:"/resource/deployment/related/virtualservice/form/check" tags:"Deployment" method:"post" summary:"check VirtualService by form" auth:"k8s-namespaceMeshVS-r"`
	ClusterID int                      `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                   `json:"namespace" v:"required#Require namespace."`
	Data      istio.VirtualServiceForm `json:"data" v:"required#Require data."`
	Action    string                   `json:"action" v:"required#Require namespace.|in:create,update"`
}

type CheckDeploymentRelatedVirtualServiceByYamlReq struct {
	g.Meta    `path:"/resource/deployment/related/virtualservice/yaml/check" tags:"Deployment" method:"post" summary:"check VirtualService by yaml" auth:"k8s-namespaceMeshVS-r"`
	ClusterID int                    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string                 `json:"namespace" v:"required#Require namespace."`
	Data      map[string]interface{} `json:"data" v:"required#Require data."`
	Action    string                 `json:"action" v:"required#Require namespace.|in:create,update"`
}

type CreateDeploymentRelatedCopyVirtualServiceReq struct {
	g.Meta          `path:"/resource/deployment/related/virtualservice/copy/create" method:"post" tags:"Deployment" auth:"k8s-namespaceMeshVS-w"`
	NewClusterID    int    `json:"newClusterId" v:"required#Require cluster." dc:"cluster id"`
	NewNamespace    string `json:"newNamespace" v:"required#Require namespace."`
	OriginClusterID int    `json:"originClusterId" v:"required#Require cluster." dc:"cluster id"`
	OriginNamespace string `json:"originNamespace" v:"required#Require namespace."`
	ResourceName    string `json:"resourceName" v:"required#Require resourcename." dc:"vs name"`
}

type ListDeploymentRelatedVirtualServiceDelegateCascaderReq struct {
	g.Meta    `path:"/resource/deployment/related/virtualservice/delegate/cascader/list" method:"get" tags:"Deployment" auth:"k8s-namespaceMeshVS-r"`
	ClusterID int `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
}

type GetDeploymentRelatedVirtualServiceRelatedDestinationRuleReq struct {
	g.Meta       `path:"/resource/deployment/related/virtualservice/related/destinationrule/get" method:"get" tags:"Deployment" sm:"VirtualService related" auth:"k8s-namespaceMeshVS-r"`
	ResourceName string `json:"resourceName" v:"required#Require resourcename." dc:"vs name"`
	ClusterID    int    `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
}

type PreviewDeploymentRelatedVirtualServiceByFormReq struct {
	g.Meta    `path:"/resource/deployment/related/virtualservice/form/preview" method:"post" tags:"Deployment" sm:"form-preview VirtualService" auth:"k8s-namespaceMeshVS-r"`
	ClusterID int                      `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace string                   `json:"namespace" v:"required#Require namespace."`
	Data      istio.VirtualServiceForm `json:"data" v:"required#Require data."`
	Action    string                   `json:"action" v:"required#Require namespace.|in:create,update"`
}

// ------------------gateway------------------

type ListDeploymentRelatedGatewayReq struct {
	g.Meta    `path:"/resource/deployment/related/gateway/list" method:"get" tags:"Deployment" sm:"" auth:"k8s-namespaceMeshGW-r"`
	ClusterID int    `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Name      string `json:"name" v:"required#Require deployment name"`
}

type ListDeploymentRelatedPodMetricsReq struct {
	g.Meta     `path:"/resource/deployment/related/pod/metrics/list" method:"get" tags:"Deployment" sm:"" `
	ClusterId  int    `json:"clusterId"`
	Namespace  string `json:"namespace"`
	ObjectName string `json:"objectName"`
}

type ListResourceDeploymentRelatedContainerReq struct {
	g.Meta    `path:"/resource/deployment/container/relate" tags:"Deployment" method:"get" summary:"list deploy related pod"`
	ClusterId int    `json:"clusterId" v:"required#Require cluster."`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}

// ListDeploymentRelatedSvcPortReq 获取deploy关联svc的端口号
type ListDeploymentRelatedSvcPortReq struct {
	g.Meta    `path:"/resource/deployment/related/svc-port/list" tags:"Deployment" method:"get" summary:"list deployment related svc port"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}

type ListDeploymentRelatedPodReq struct {
	g.Meta    `path:"/resource/deployment/related/pod/list" method:"get" tags:"Deployment" sm:"" `
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}
