package resource

import (
	"github.com/gogf/gf/v2/frame/g"

	"tt-cloud-enterprise/tools/k8s"
)

// ListResourceStatefulSetWithApplicationReq 用于应用管理页的statefulset相关列表
type ListResourceStatefulSetWithApplicationReq struct {
	g.Meta    `path:"/resource/statefulset/list" tags:"statefulsets" method:"post" summary:"list resource statefulset"`
	ClusterID int    `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace string `json:"namespace" v:"required#Require namespace." dc:"namespace"`
	Search    string `json:"search"  dc:"search"`
	Page      int    `json:"page" d:"1"`
	Size      int    `json:"size" d:"10"`
	// LabelSelectorMap map[string]k8s.LabelSelector `json:"label_selector_map"`
	LabelSelector []k8s.LabelSelector `json:"labelSelector" dc:"labelSelector"`
}

// RolloutRestartResourceStatefulSetWithApplicationReq 滚动重启statefulset
type RolloutRestartResourceStatefulSetWithApplicationReq struct {
	g.Meta    `path:"/resource/statefulset/restart" tags:"statefulsets" method:"post" summary:"rollout restart statefulset" auth:"k8s-applicationList-w"`
	ClusterID int    `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace string `json:"namespace" v:"required#Require namespace." dc:"namespace"`
	Name      string `json:"name" v:"required#Require namespace." dc:"namespace"`
}

// GetResourceStatefulSetDetailReq 用于应用管理页的statefulset的详细信息
type GetResourceStatefulSetDetailReq struct {
	g.Meta    `path:"/resource/statefulset/get" tags:"statefulsets" method:"get" summary:"get resource statefulset detail"`
	ClusterID int    `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace string `json:"namespace" v:"required#Require namespace." dc:"namespace"`
	Name      string `json:"name"  v:"required#Require name." dc:"name"`
}

type GetStsDetailReq struct {
	g.Meta    `path:"/resource/statefulset/native-json/get" tags:"statefulsets" method:"get" summary:"get statefulset json"`
	ClusterID int    `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace string `json:"namespace" v:"required#Require namespace." dc:"namespace"`
	Name      string `json:"name" v:"required#Require name." dc:"name"`
}

// DeleteResourceStatefulSetRelatedPodReq 删除statefulset关联的pod
type DeleteResourceStatefulSetRelatedPodReq struct {
	g.Meta    `path:"/resource/statefulset/pod/delete" tags:"statefulsets" method:"delete" summary:"delete resource statefulset related pod" auth:"k8s-applicationList-w"`
	ClusterID int    `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace string `json:"namespace" v:"required#Require namespace." dc:"namespace"`
	Name      string `json:"name"  v:"required#Require name." dc:"name"`
}

// ListResourceStatefulSetRelatedPodReq 展示statefulset关联的pod
type ListResourceStatefulSetRelatedPodReq struct {
	g.Meta    `path:"/resource/statefulset/pod/list" tags:"statefulsets" method:"get" summary:"list resource statefulset related pod"`
	ClusterID int    `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace string `json:"namespace" v:"required#Require namespace." dc:"namespace"`
	Name      string `json:"name"  v:"required#Require name." dc:"name"`
}

// ListResourceStatefulSetRelatedBriefStatisticsReq 展示statefulset关联的资源概览
type ListResourceStatefulSetRelatedBriefStatisticsReq struct {
	g.Meta    `path:"/resource/statefulset/related/brief-statistics/list" tags:"statefulsets" method:"get" summary:"list statefulset related brief-statistics"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Name      string `json:"name" v:"required#Require name."`
}

// GetStatefulSetReplicasCountReq 获取sts的当前副本数
type GetStatefulSetReplicasCountReq struct {
	g.Meta    `path:"/resource/statefulset/replicas-count/get" tags:"statefulsets" method:"get" summary:"get statefulset replicas count"`
	ClusterId int    `json:"clusterId" v:"required# require cluster id"`
	Namespace string `json:"namespace" v:"required# require namespace"`
	Name      string `json:"name" v:"required# require name"`
}

// SetStatefulSetReplicasCountReq 设置sts的当前副本数
type SetStatefulSetReplicasCountReq struct {
	g.Meta    `path:"/resource/statefulset/replicas-count/set" tags:"statefulsets" method:"post" summary:"set statefulset replicas count"`
	ClusterId int    `json:"clusterId" v:"required# require cluster id"`
	Namespace string `json:"namespace" v:"required# require namespace"`
	Name      string `json:"name" v:"required# require name"`
	Replicas  int    `json:"replicas"`
}

// ListStatefulSetRelatedPodReq 展示当前sts关联的pod信息
type ListStatefulSetRelatedPodReq struct {
	g.Meta    `path:"/resource/statefulset/related/pod" tags:"statefulsets" method:"get" summary:"list statefulset related pods"`
	ClusterId int    `json:"clusterId" v:"required# require cluster id"`
	Namespace string `json:"namespace" v:"required# require namespace"`
	Name      string `json:"name" v:"required# require name"`
}

// ListStatefulSetRelatedServiceReq 展示当前sts关联的svc信息
type ListStatefulSetRelatedServiceReq struct {
	g.Meta    `path:"/resource/statefulset/related/service" tags:"statefulsets" method:"get" summary:"list statefulset related service"`
	ClusterId int    `json:"clusterId" v:"required# require cluster id"`
	Namespace string `json:"namespace" v:"required# require namespace"`
	Name      string `json:"name" v:"required# require name"`
}

type ListStatefulSetRelatedNativeServiceReq struct {
	g.Meta    `path:"/resource/statefulset/related/native-json/service" tags:"statefulsets" method:"get" summary:"list statefulset related native service"`
	ClusterId int    `json:"clusterId" v:"required# require cluster id"`
	Namespace string `json:"namespace" v:"required# require namespace"`
	Name      string `json:"name" v:"required# require name"`
}

// ListStatefulSetRelatedHpaReq 展示当前sts关联的hpa信息
type ListStatefulSetRelatedHpaReq struct {
	g.Meta    `path:"/resource/statefulset/related/hpa" tags:"statefulsets" method:"get" summary:"list statefulset related hpa"`
	ClusterId int    `json:"clusterId" v:"required# require cluster id"`
	Namespace string `json:"namespace" v:"required# require namespace"`
	Name      string `json:"name" v:"required# require name"`
}

// ListStatefulSetRelatedConfigmapReq 展示当前sts关联的configmap信息
type ListStatefulSetRelatedConfigmapReq struct {
	g.Meta    `path:"/resource/statefulset/related/configmap" tags:"statefulsets" method:"get" summary:"list statefulset related configmap"`
	ClusterId int    `json:"clusterId" v:"required# require cluster id"`
	Namespace string `json:"namespace" v:"required# require namespace"`
	Name      string `json:"name" v:"required# require name"`
}

// ListStatefulSetRelatedSecretReq 展示当前sts关联的secret信息
type ListStatefulSetRelatedSecretReq struct {
	g.Meta    `path:"/resource/statefulset/related/secret" tags:"statefulsets" method:"get" summary:"list statefulset related secret"`
	ClusterId int    `json:"clusterId" v:"required# require cluster id"`
	Namespace string `json:"namespace" v:"required# require namespace"`
	Name      string `json:"name" v:"required# require name"`
}

// ListStatefulSetRelatedPvcReq 展示当前sts关联的pvc信息
type ListStatefulSetRelatedPvcReq struct {
	g.Meta    `path:"/resource/statefulset/related/pvc" tags:"statefulsets" method:"get" summary:"list statefulset related pvc"`
	ClusterId int    `json:"clusterId" v:"required# require cluster id"`
	Namespace string `json:"namespace" v:"required# require namespace"`
	Name      string `json:"name" v:"required# require name"`
}

// ListStatefulSetRelatedVirtualServiceReq 展示当前sts关联的vs信息
type ListStatefulSetRelatedVirtualServiceReq struct {
	g.Meta    `path:"/resource/statefulset/related/virtaulservice" tags:"statefulsets" method:"get" summary:"list statefulset related vs"`
	ClusterId int    `json:"clusterId" v:"required# require cluster id"`
	Namespace string `json:"namespace" v:"required# require namespace"`
	Name      string `json:"name" v:"required# require name"`
}

// ListStatefulSetRelatedDestinationRuleReq 展示当前sts关联的dr信息
type ListStatefulSetRelatedDestinationRuleReq struct {
	g.Meta    `path:"/resource/statefulset/related/destinationrule" tags:"statefulsets" method:"get" summary:"list statefulset related dr"`
	ClusterId int    `json:"clusterId" v:"required# require cluster id"`
	Namespace string `json:"namespace" v:"required# require namespace"`
	Name      string `json:"name" v:"required# require name"`
}

// ListStatefulSetRelatedGatewayReq 展示当前sts关联的gw信息
type ListStatefulSetRelatedGatewayReq struct {
	g.Meta    `path:"/resource/statefulset/related/gateway" tags:"statefulsets" method:"get" summary:"list statefulset related gw"`
	ClusterId int    `json:"clusterId" v:"required# require cluster id"`
	Namespace string `json:"namespace" v:"required# require namespace"`
	Name      string `json:"name" v:"required# require name"`
}

// SubscribeStatefulSetNotifyReq 订阅/取消订阅 当前sts
type SubscribeStatefulSetNotifyReq struct {
	g.Meta    `path:"/resource/statefulset/subscribe" tags:"statefulsets" method:"post" summary:"订阅statefulsets"`
	ClusterId int    `json:"clusterId" v:"required# require cluster id"`
	Namespace string `json:"namespace" v:"required# require namespace"`
	Name      string `json:"name" v:"required# require name"`
	// true: 订阅,false: 取消订阅
	IsSubscribe bool `json:"isSubscribe" v:"required# require isSubscribe"`
}

// GetSubscribedStatefulSetReq 获取用户对statefulset的订阅状态
type GetSubscribedStatefulSetReq struct {
	g.Meta    `path:"/resource/statefulset/subscribe/get" tags:"statefulsets" method:"get" summary:"get subscribe statefulset"`
	ClusterId int    `json:"clusterId" v:"required# require cluster id"`
	Namespace string `json:"namespace" v:"required# require namespace"`
	Name      string `json:"name" v:"required# require name"`
}

type ListResourceStatefulSetRelatedContainerReq struct {
	g.Meta    `path:"/resource/statefulset/container/relate" tags:"statefulsets" method:"get" summary:"list sts related pod"`
	ClusterId int    `json:"clusterId" v:"required#Require cluster."`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}

// ListStatefulSetRelatedSvcPortReq 获取sts关联svc的端口号
type ListStatefulSetRelatedSvcPortReq struct {
	g.Meta    `path:"/resource/statefulset/related/svc-port/list" tags:"StatefulSet" method:"get" summary:"list statefulset related svc port"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}
