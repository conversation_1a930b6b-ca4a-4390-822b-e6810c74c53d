package workspace

import "github.com/gogf/gf/v2/frame/g"

type UserWorkspaceProfileReq struct {
	g.Meta `path:"/workspace/user/profile" tags:"Workspace" method:"get" summary:"get user workspace profile"`
}

type ListUserAuthWithK8sReq struct {
	g.Meta `path:"/workspace/user/auth/k8s/list" tags:"Workspace" method:"get" summary:"list user auth k8s"`
}

type ListUserAuthWithKubectlProxyReq struct {
	g.Meta `path:"/workspace/user/auth/kubectl-proxy/list" tags:"Workspace" method:"get" summary:"list user auth kubectl-proxy"`
}

type ListUserAuthWithKubectlWebShellReq struct {
	g.Meta `path:"/workspace/user/auth/kubectl-webshell/list" tags:"Workspace" method:"get" summary:"list user auth kubectl-webshell"`
}

type DownloadUserKubeconfigReq struct {
	g.Meta    `path:"/workspace/user/auth/kubeconfig/download" tags:"Workspace" method:"post" summary:"download user kubeconfig"`
	ClusterId int `json:"clusterId"`
}

type MergeDownloadUserKubeconfigReq struct {
	g.Meta `path:"/workspace/user/auth/kubeconfig/merge-download" tags:"Workspace" method:"post" summary:"merge download user kubeconfig"`
}

type ListUserAppReq struct {
	g.Meta `path:"/workspace/user/app/list" tags:"Workspace" method:"get" summary:"list user app"`
	Search string `json:"search"`
	Kind   string `json:"kind"`
}

type ListUserSubscribeAppReq struct {
	g.Meta `path:"/workspace/user/subscribe-app/list" tags:"Workspace" method:"get" summary:"list user subscribe app"`
	Search string `json:"search"`
	Kind   string `json:"kind"`
}

type SubscribeUserAppReq struct {
	g.Meta    `path:"/workspace/user/app/subscribe" tags:"Workspace" method:"post" summary:"subscribe app"`
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
	Kind      string `json:"kind"`
}

type UnSubscribeUserAppReq struct {
	g.Meta    `path:"/workspace/user/app/unsubscribe" tags:"Workspace" method:"post" summary:"unsubscribe app"`
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
	Kind      string `json:"kind"`
}
