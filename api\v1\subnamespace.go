package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"tt-cloud-enterprise/internal/model/dto/subnamespace"
)

//子环境相关api

// ListSubNamespaceReq 展示子环境列表
type ListSubNamespaceReq struct {
	g.Meta    `path:"/subnamespace/list" method:"get" tags:"subNamespace" summary:"list sub namespace"`
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Search    string `json:"search"  dc:"search"`
	Page      int    `json:"page" d:"1"`
	Size      int    `json:"size" d:"10"`
}

// DeleteSubNamespaceReq 删除某个子环境
type DeleteSubNamespaceReq struct {
	g.Meta    `path:"/subnamespace/delete" method:"delete" tags:"subNamespace" summary:"delete sub namespace" auth:"k8s-subNamespaceList-w"`
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
}

// CreateSubNamespaceReq 创建子环境
type CreateSubNamespaceReq struct {
	g.<PERSON>a          `path:"/subnamespace/create" method:"post" tags:"subNamespace" summary:"create sub namespace" auth:"k8s-subNamespaceList-w"`
	ClusterId       int    `json:"clusterId"`
	Namespace       string `json:"targetNamespace"`
	OriginNamespace string `json:"originNamespace"`
	Describe        string `json:"describe"`
}

// ListSubNamespaceCopyResourceReq 根据kind展示可以被复制的资源列表
type ListSubNamespaceCopyResourceReq struct {
	g.Meta    `path:"/subnamespace/resource-name/list" method:"get" tags:"subNamespace" summary:"list resource name"`
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Kind      string `json:"kind"`
}

// CopySubNamespaceResourceReq 从基准环境向子环境复制指定的k8s资源  configmap/secret/serviceaccount
type CopySubNamespaceResourceReq struct {
	g.Meta          `path:"/subnamespace/resource/copy" method:"post" tags:"subNamespace" summary:"list resource name" auth:"k8s-subNamespaceList-w"`
	ClusterId       int                     `json:"clusterId"`
	OriginNamespace string                  `json:"originNamespace" v:"required# Require originNamespace"`
	TargetNamespace string                  `json:"targetNamespace" v:"required# Require targetNamespace"`
	Resources       []subnamespace.Resource `json:"resources"`
}
