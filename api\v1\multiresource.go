package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	multiresourceDto "tt-cloud-enterprise/internal/model/dto/multiresource"
)

type ListLicenseInfoReq struct {
	g.Meta `path:"/multi-cluster-search/license-info/list" tags:"multiresource" method:"get" summary:"list license info"`
}

type ListLicenseInfoRes struct {
	Clusters   []*multiresourceDto.Cluster  `json:"clusters"`
	Namespaces []string                     `json:"namespaces"`
	Resources  []*multiresourceDto.Resource `json:"resources"`
}

type ListMultiClusterSearchReq struct {
	g.Meta `path:"/multi-cluster-search/list" tags:"multiresource" method:"post" summary:"list multi cluster search"`
	multiresourceDto.QueryMultiResourceDto
}

type ListMultiClusterSearchHistoryReq struct {
	g.Meta `path:"/multi-cluster-search/history/list" tags:"multiresource" method:"get" summary:"list multi cluster search history"`
}

type SaveMultiClusterSearchHistoryReq struct {
	g.Meta `path:"/multi-cluster-search/history/save" tags:"multiresource" method:"post" summary:"save multi cluster search history"`
	multiresourceDto.QueryMultiResourceHistoryDto
}

type DeleteMultiClusterSearchHistoryReq struct {
	g.Meta `path:"/multi-cluster-search/history/delete" tags:"multiresource" method:"delete" summary:"delete multi cluster search history"`
	Name   string `json:"name"`
}

// ListMultiClusterRelatedResourceReq 查询当前资源的关联资源
type ListMultiClusterRelatedResourceReq struct {
	g.Meta `path:"/multi-cluster-search/resource/relate" tags:"multiresource" method:"post" summary:"list multi cluster related resource"`
	multiresourceDto.Object
}

type ListPodRelatedContainerReq struct {
	g.Meta    `path:"/multi-cluster-search/pod/container/relate" tags:"multiresource" method:"get" summary:"list pod related container"`
	ClusterId int    `json:"clusterId"`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}
