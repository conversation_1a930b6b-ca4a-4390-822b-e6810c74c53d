package portforward

import "github.com/gogf/gf/v2/frame/g"

type CreateLinkByDeploymentReq struct {
	g.Meta     `path:"/portforward/deployment/create" method:"post" summary:"创建端口转发链接" tags:"portforward" auth:"k8s-namespaceWorkload-w"`
	ClusterId  int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace  string `json:"namespace" v:"required#Require namespace"`
	Pod        string `json:"pod" v:"required#Require podName"`
	TargetPort int    `json:"targetPort" v:"required#Require targetPort"`
	Name       string `json:"name"`
}

type DeleteLinkByDeploymentReq struct {
	g.Meta     `path:"/portforward/deployment/delete" method:"delete" summary:"删除端口转发链接" tags:"portforward" auth:"k8s-namespaceWorkload-w"`
	ClusterId  int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace  string `json:"namespace" v:"required#Require namespace"`
	Pod        string `json:"pod" v:"required#Require podName"`
	TargetPort int    `json:"targetPort" v:"required#Require targetPort"`
	ProxyPort  int    `json:"proxyPort"`
}

type ListLinkByDeploymentReq struct {
	g.Meta    `path:"/portforward/deployment/list" method:"get" summary:"展示端口转发链接" tags:"portforward"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace string `json:"namespace" v:"required#Require namespace"`
	Pod       string `json:"pod" v:"required#Require podName"`
}

type RebuildLinkByDeploymentReq struct {
	g.Meta     `path:"/portforward/deployment/rebuild" method:"post" summary:"重建端口转发链接" tags:"portforward" auth:"k8s-namespaceWorkload-w"`
	ClusterId  int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace  string `json:"namespace" v:"required#Require namespace"`
	Pod        string `json:"pod" v:"required#Require podName"`
	TargetPort int    `json:"targetPort" v:"required#Require targetPort"`
	ProxyPort  int    `json:"proxyPort"`
}

type CreateLinkByStatefulSetReq struct {
	g.Meta     `path:"/portforward/statefulset/create" method:"post" summary:"创建端口转发链接" tags:"portforward" auth:"k8s-namespaceWorkload-w"`
	ClusterId  int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace  string `json:"namespace" v:"required#Require namespace"`
	Pod        string `json:"pod" v:"required#Require podName"`
	TargetPort int    `json:"targetPort" v:"required#Require targetPort"`
	Name       string `json:"name"`
}

type DeleteLinkByStatefulSetReq struct {
	g.Meta     `path:"/portforward/statefulset/delete" method:"delete" summary:"删除端口转发链接" tags:"portforward" auth:"k8s-namespaceWorkload-w"`
	ClusterId  int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace  string `json:"namespace" v:"required#Require namespace"`
	Pod        string `json:"pod" v:"required#Require podName"`
	TargetPort int    `json:"targetPort" v:"required#Require targetPort"`
	ProxyPort  int    `json:"proxyPort"`
}

type ListLinkByStatefulSetReq struct {
	g.Meta    `path:"/portforward/statefulset/list" method:"get" summary:"展示端口转发链接" tags:"portforward"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace string `json:"namespace" v:"required#Require namespace"`
	Pod       string `json:"pod" v:"required#Require podName"`
}

type RebuildLinkByStatefulSetReq struct {
	g.Meta     `path:"/portforward/statefulset/rebuild" method:"post" summary:"重建端口转发链接" tags:"portforward" auth:"k8s-namespaceWorkload-w"`
	ClusterId  int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace  string `json:"namespace" v:"required#Require namespace"`
	Pod        string `json:"pod" v:"required#Require podName"`
	TargetPort int    `json:"targetPort" v:"required#Require targetPort"`
	ProxyPort  int    `json:"proxyPort"`
}

type CreateLinkByPodReq struct {
	g.Meta     `path:"/portforward/pod/create" method:"post" summary:"创建端口转发链接" tags:"portforward" auth:"k8s-namespacePods-w"`
	ClusterId  int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace  string `json:"namespace" v:"required#Require namespace"`
	Pod        string `json:"pod" v:"required#Require podName"`
	TargetPort int    `json:"targetPort" v:"required#Require targetPort"`
	Name       string `json:"name"`
}

type DeleteLinkByPodReq struct {
	g.Meta     `path:"/portforward/pod/delete" method:"delete" summary:"删除端口转发链接" tags:"portforward" auth:"k8s-namespacePods-w"`
	ClusterId  int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace  string `json:"namespace" v:"required#Require namespace"`
	Pod        string `json:"pod" v:"required#Require podName"`
	TargetPort int    `json:"targetPort" v:"required#Require targetPort"`
	ProxyPort  int    `json:"proxyPort"`
}

type ListLinkByPodReq struct {
	g.Meta    `path:"/portforward/pod/list" method:"get" summary:"展示端口转发链接" tags:"portforward"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace string `json:"namespace" v:"required#Require namespace"`
	Pod       string `json:"pod" v:"required#Require podName"`
}

type RebuildLinkByPodReq struct {
	g.Meta     `path:"/portforward/pod/rebuild" method:"post" summary:"重建端口转发链接" tags:"portforward" auth:"k8s-namespacePods-w"`
	ClusterId  int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace  string `json:"namespace" v:"required#Require namespace"`
	Pod        string `json:"pod" v:"required#Require podName"`
	TargetPort int    `json:"targetPort" v:"required#Require targetPort"`
	ProxyPort  int    `json:"proxyPort"`
}

type DeleteLinkByManagementReq struct {
	g.Meta     `path:"/portforward/management/delete" method:"delete" summary:"删除端口转发链接" tags:"portforward" auth:"k8s-opsManagement-w"`
	ClusterId  int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace  string `json:"namespace" v:"required#Require namespace"`
	Pod        string `json:"pod" v:"required#Require podName"`
	TargetPort int    `json:"targetPort" v:"required#Require targetPort"`
	ProxyPort  int    `json:"proxyPort"`
}

type ListLinkByManagementReq struct {
	g.Meta    `path:"/portforward/management/list" method:"get" summary:"展示端口转发链接" tags:"portforward"`
	ClusterId int    `json:"clusterId" `
	Namespace string `json:"namespace" `
	Search    string `json:"search" `
	Page      int    `json:"page" d:"1" `
	Size      int    `json:"size" d:"10" `
}

type RebuildLinkByManagementReq struct {
	g.Meta     `path:"/portforward/management/rebuild" method:"post" summary:"重建端口转发链接" tags:"portforward" auth:"k8s-opsManagement-w"`
	ClusterId  int    `json:"clusterId" v:"required#Require clusterId"`
	Namespace  string `json:"namespace" v:"required#Require namespace"`
	Pod        string `json:"pod" v:"required#Require podName"`
	TargetPort int    `json:"targetPort" v:"required#Require targetPort"`
	ProxyPort  int    `json:"proxyPort"`
}
