package resource

import (
	"github.com/gogf/gf/v2/frame/g"
	corev1 "k8s.io/api/core/v1"
	"tt-cloud-enterprise/internal/model/dto/k8s"
)

// ListResourceNodeRelatePodReq node关联pod的列表
type ListResourceNodeRelatePodReq struct {
	g.Meta    `path:"/resource/node/pod/list" tags:"nodes" method:"get" summary:"list node related pod" `
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Node      string `json:"node" v:"required#Require node."`
}

type ListResourceNodeRelatePodRes struct {
	Pods         []corev1.Pod `json:"pods"`
	NodeTotalCpu int64        `json:"nodeTotalCpu"`
	NodeTotalMem int64        `json:"nodeTotalMem"`
}

// GetResourceNodeSchedulableReq 获取node是否可调度
type GetResourceNodeSchedulableReq struct {
	g.Meta    `path:"/resource/node/is-schedulable" tags:"nodes" method:"get" summary:"check node schedulable"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Node      string `json:"node" v:"required#Require node."`
}

// GetResourceNodeScheduleableRes
type GetResourceNodeScheduleableRes struct {
	IsSchedulable bool `json:"isSchedulable"`
}

// UpdateResourceNodeScheduleReq 修改node的调度能力
type UpdateResourceNodeScheduleReq struct {
	g.Meta      `path:"/resource/node/schedule" tags:"nodes" method:"post" summary:"update node schedule" auth:"k8s-opsManagement-w"`
	ClusterId   int    `json:"clusterId" v:"required#Require clusterId."`
	Node        string `json:"node" v:"required#Require node."`
	Schedulable bool   `json:"schedulable" v:"required#Require schedulable."`
}

// DrainResourceNodeRelatedPodReq 驱逐node上的pod
type DrainResourceNodeRelatedPodReq struct {
	g.Meta              `path:"/resource/node/drain" tags:"nodes" method:"post" summary:"drain node" auth:"k8s-opsManagement-w"`
	ClusterId           int    `json:"clusterId" v:"required#Require clusterId."`
	Node                string `json:"node" v:"required#Require node."`
	Timeout             int    `json:"timeout" v:"required#Require timeout."`
	GracePeriod         int    `json:"gracePeriod" v:"required#Require gracePeriod."`
	IgnoreAllDaemonSets bool   `json:"ignoreAllDaemonSets" d:"true"`
	DeleteEmptyDirData  bool   `json:"deleteEmptyDirData" d:"true"`
}

// ListResourceNodeLabelReq node的label列表
type ListResourceNodeLabelReq struct {
	g.Meta    `path:"/resource/node/label/list" tags:"nodes" method:"get" summary:"list node label" `
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Node      string `json:"node" v:"required#Require node."`
}

// GetResourceNodeDetailReq node详情
type GetResourceNodeDetailReq struct {
	g.Meta    `path:"/resource/node/get" tags:"nodes" method:"get" summary:"list node label" `
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Node      string `json:"node" v:"required#Require node."`
}

type ListResourceNodeLabelRes struct {
	Label map[string]string `json:"label"`
}

// AddResourceNodeLabelReq 对node添加label
type AddResourceNodeLabelReq struct {
	g.Meta    `path:"/resource/node/label/add" tags:"nodes" method:"post" summary:"add node label" auth:"k8s-opsManagement-w"`
	ClusterId int               `json:"clusterId" v:"required#Require clusterId."`
	Node      string            `json:"node" v:"required#Require node."`
	Label     map[string]string `json:"label" v:"required#Require node."`
}

// DeleteResourceNodeLabelReq 对node删除label
type DeleteResourceNodeLabelReq struct {
	g.Meta    `path:"/resource/node/label/delete" tags:"nodes" method:"delete" summary:"delete node label" auth:"k8s-opsManagement-w"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Node      string `json:"node" v:"required#Require node."`
	LabelKey  string `json:"labelKey" v:"required#Require label_key."`
}

// DeleteResourceNodePodReq 删除node上的pod
type DeleteResourceNodePodReq struct {
	g.Meta    `path:"/resource/node/pod/delete" tags:"nodes" method:"delete" summary:"delete node pod" auth:"k8s-opsManagement-w"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Node      string `json:"node" v:"required#Require node."`
	Pod       string `json:"pod" v:"required#Require pod."`
}

// ListResourceNodePodMetricsReq 查看node上pod的metrics信息
type ListResourceNodePodMetricsReq struct {
	g.Meta    `path:"/resource/node/pod-metrics/list" tags:"nodes" method:"post" summary:"list node pod metrics"`
	ClusterId int    `json:"clusterId" v:"required#Require clusterId."`
	Node      string `json:"node" v:"required#Require node."`
}

type ListResourceNodePodMetricsRes struct {
	PodMetrics []*k8s.PodNameAndMetrics `json:"podMetrics"`
}
