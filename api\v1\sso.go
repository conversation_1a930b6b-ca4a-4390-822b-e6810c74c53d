package v1

import "github.com/gogf/gf/v2/frame/g"

// AuthSsoLoginReq 单点登录
type AuthSsoLoginReq struct {
	g.Meta `path:"/auth/sso/login" method:"post" tags:"Sso" sm:"sso-auth"`

	//ticket参数在url上
	Ticket string `json:"ticket" v:"required#Require login ticket." dc:"ticket"`
}

// AuthSsoLogoutReq 单点登出
type AuthSsoLogoutReq struct {
	g.Meta `path:"/auth/sso/logout" method:"post" tags:"JwtAuth" sm:"sso-auth"`
}
