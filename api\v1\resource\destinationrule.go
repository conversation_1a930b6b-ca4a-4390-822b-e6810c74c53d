package resource

import "github.com/gogf/gf/v2/frame/g"

// ----------------------------------------------- destinationRule-----------------------------------------------
type ListResourceDestinationRuleReq struct {
	g.Meta    `path:"/resource/destinationrule/list" method:"get" tags:"destinationrule" auth:"k8s-namespaceMeshDR-r"`
	ClusterID int    `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Page      int    `json:"page" d:"1"`
	Size      int    `json:"size" d:"10"`
	Search    string `json:"search" dc:"dr name for searching destinationRule"`
}

type GetDestinationRuleDetailsReq struct {
	g.Meta       `path:"/resource/destinationrule/get" method:"get" tags:"destinationrule" sm:"destinationrule details" auth:"k8s-namespaceMeshDR-r"`
	ResourceName string `json:"resource_name" v:"required#Require resourcename." dc:"dr name"`
	ClusterID    int    `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
}

type GetRelatedPodsReq struct {
	g.Meta    `path:"/resource/related/destinationrule/pod/list" method:"get" tags:"destinationrule" sm:"pods raleted destinationrule" auth:"k8s-namespaceMeshDR-r"`
	Host      string `json:"host"  dc:"host in destinationrule"`
	ClusterID int    `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace string `json:"namespace" v:"required#Require namespace."`
}

type CreateCopyDestinationRuleReq struct {
	g.Meta          `path:"/resource/destinationrule/copy/create" method:"post" tags:"destinationrule" auth:"k8s-namespaceMeshDR-w"`
	NewClusterID    int    `json:"new_cluster_id" v:"required#Require cluster." dc:"cluster id"`
	NewNamespace    string `json:"new_namespace" v:"required#Require namespace."`
	OriginClusterID int    `json:"origin_cluster_id" v:"required#Require cluster." dc:"cluster id"`
	OriginNamespace string `json:"origin_namespace" v:"required#Require namespace."`
	ResourceName    string `json:"resource_name" v:"required#Require resourcename." dc:"dr name"`
}

type CreateDestinationRuleReq struct {
	g.Meta     `path:"/resource/destinationrule/create" method:"post" tags:"destinationrule" auth:"k8s-namespaceMeshDR-w"`
	Data       interface{} `json:"data" v:"required#Require test." dc:"text for k8s"`
	ClusterID  int         `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace  string      `json:"namespace" v:"required#Require namespace."`
	Annotation string      `json:"annotation" v:"required#Require annotation."`
}

type UpdateDestinationRuleReq struct {
	g.Meta     `path:"/resource/destinationrule/update" method:"post" tags:"destinationrule" auth:"k8s-namespaceMeshDR-w"`
	Data       interface{} `json:"data" v:"required#Require test." dc:"text for k8s"`
	ClusterID  int         `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace  string      `json:"namespace" v:"required#Require namespace."`
	Annotation string      `json:"annotation" v:"required#Require annotation."`
}

type DeleteDestinationRuleReq struct {
	g.Meta       `path:"/resource/destinationrule/delete" method:"delete" tags:"destinationrule" auth:"k8s-namespaceMeshDR-w"`
	ResourceName string `json:"resource_name" v:"required#Require resourcename." dc:"dr name"`
	ClusterID    int    `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
}

type ListDestinationRuleHistoryReq struct {
	g.Meta       `path:"/resource/destinationrule/history/list" method:"get" tags:"destinationrule" sm:"destinationrule history" auth:"k8s-namespaceMeshDR-r"`
	ResourceName string `json:"resource_name" v:"required#Require resourcename." dc:"dr name"`
	ClusterID    int    `json:"cluster_id" v:"required#Require cluster." dc:"cluster id"`
	Namespace    string `json:"namespace" v:"required#Require namespace."`
}

type CreateDestinationRuleCheckReq struct {
	g.Meta     `path:"/resource/destinationrule/create/check" method:"post" tags:"destinationrule" auth:"k8s-namespaceMeshDR-w"`
	Data       interface{} `json:"data" v:"required#Require test." dc:"text for k8s"`
	ClusterID  int         `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace  string      `json:"namespace" v:"required#Require namespace."`
	Annotation string      `json:"annotation" v:"required#Require annotation."`
}

type UpdateDestinationRuleCheckReq struct {
	g.Meta     `path:"/resource/destinationrule/update/check" method:"post" tags:"destinationrule" auth:"k8s-namespaceMeshDR-w"`
	Data       interface{} `json:"data" v:"required#Require test." dc:"text for k8s"`
	ClusterID  int         `json:"clusterId" v:"required#Require cluster." dc:"cluster id"`
	Namespace  string      `json:"namespace" v:"required#Require namespace."`
	Annotation string      `json:"annotation" v:"required#Require annotation."`
}
