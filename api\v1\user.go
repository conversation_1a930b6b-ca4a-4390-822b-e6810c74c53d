package v1

import (
	"tt-cloud-enterprise/internal/consts/praconsts"
	"tt-cloud-enterprise/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

type UserGetInfoReq struct {
	g.Meta `path:"/user/profile" method:"get" tags:"User" sm:"get user profile"`
}

type UserGetInfoRes struct {
	Id          int                    `json:"id"`
	IdentityKey string                 `json:"identityKey"`
	Payload     string                 `json:"payload"`
	Profile     map[string]interface{} `json:"profile"`

	//k8s资源权限
	K8sAuthData map[string]map[string]map[string]string `json:"k8s_auth"`

	//view权限
	ViewAuthData []string `json:"view_auth"`

	//kubectl权限
	KubectlRole KubectlAuthData `json:"kubectl_auth"`

	//kubeconfig权限
	KubeconfigRole KubeconfigAuthData `json:"kubeconfig_auth"`
}

type UserGetReq struct {
	g.Meta `path:"/user/get" method:"get" tags:"User" sm:"get user info"`
	Uid    int `json:"uid" v:"required#Require gender" auth:"management-user-r"`
}

type K8sClusterNamespaceAuthData struct {
	//key: namespace  ,  value: 这个namespace下所有的权限数据
	NamespaceData map[string][]K8sResourceData `json:"namespace"`
}

type K8sResourceData struct {
	Kind      praconsts.K8sRsKind `json:"kind"`
	Authority string              `json:"authority"`
}

type KubectlAuthData struct {
	KubectlClusterRole   []entity.KubectlRole `json:"cluster"`
	KubectlNamespaceRole []entity.KubectlRole `json:"namespace"`
}

type KubeconfigAuthData struct {
	KubeconfigGlobalRole    []entity.CloudK8SAuthRole `json:"cluster"`
	KubeconfigNamespaceRole []entity.CloudK8SAuthRole `json:"namespace"`
}

type UpdateProfileReq struct {
	g.Meta   `path:"/user/profile/:uid" method:"post" tags:"User" sm:"update user profile" auth:"management-user-w"`
	Username string `json:"username" v:"required#Require username" description:"username"`
	NickName string `json:"nickName" v:"required#Require nick_name" description:"nick name"`
	Gender   string `json:"gender" v:"required#Require gender"   description:"gender"`
}

type UpdateProfileRes struct {
	entity.User
}

type UpdatePasswordReq struct {
	g.Meta      `path:"/user/password/update/:uid" method:"post" tags:"User" sm:"update user password" auth:"management-user-w"`
	OldPassword string `json:"oldPassword" v:"required#Require old password" dc:"old password"`
	NewPassword string `json:"newPassword" v:"required#Require new password" dc:"new password"`
}

type UpdatePasswordRes struct {
}

type SetAdminReq struct {
	g.Meta  `path:"/user/set-admin" method:"post" tags:"User" sm:"set user admin" auth:"management-user-w"`
	UserId  int  `json:"userId" v:"required#Require user id" dc:"user id"`
	IsAdmin bool `json:"isAdmin" v:"required#Require is_admin" dc:"is admin"`
}

type SetAdminRes struct {
}

type SetIsActiveReq struct {
	g.Meta   `path:"/user/set-active" method:"post" tags:"User" sm:"set user admin" auth:"management-user-w"`
	UserId   int  `json:"userId" v:"required#Require user id" dc:"user id"`
	IsActive bool `json:"isActive" v:"required#Require is_active" dc:"is active"`
}

type SetIsActiveRes struct {
}

type SetClusterAdminReq struct {
	g.Meta         `path:"/user/set-cluster-admin" method:"post" tags:"User" sm:"set user cluster admin" auth:"management-user-w"`
	UserId         int  `json:"userId" v:"required#Require user id" dc:"user id"`
	IsClusterAdmin bool `json:"isClusterAdmin" v:"required#Require is_cluster_admin" dc:"is cluster admin"`
}

type SetClusterAdminRes struct {
}

// v:"required#Require namespaces"
type SetNamespacesReq struct {
	g.Meta     `path:"/user/set-namespace-scope" tags:"User" method:"post" summary:"set namespace scope"`
	UserId     int      `json:"userId" v:"required#Require user id" dc:"user id"`
	Namespaces []string `json:"namespaces"  dc:"namespace list"`
}

type SetNamespacesRes struct {
}

type DeleteUserReq struct {
	g.Meta `path:"/user/:uid" method:"delete" tags:"User" sm:"delete user" auth:"management-user-w"`
}

type DeleteUserRes struct {
}

type UserListReq struct {
	g.Meta `path:"/user/list" method:"get" tags:"User" sm:"list users"`
	ListReq
}

type UserCountReq struct {
	g.Meta `path:"/user/count" method:"get" tags:"User" summary:"user count"`
}

type UserCountRes struct {
	Count int `json:"count"`
}

type UserOpCountReq struct {
	g.Meta `path:"/user/operation-count" method:"get" tags:"User" summary:"user count"`
}

type UserOpCountRes struct {
	Count int `json:"count"`
}

type ListUserResourceSubscribeReq struct {
	g.Meta `path:"/user/resource-subscribe/list" method:"get" tags:"User" summary:"list user resource subscribe"`
}
