package eventcenter

import (
	"context"
	"fmt"
	"sync"
	"tt-cloud-enterprise/internal/model/dto"
	"tt-cloud-enterprise/internal/service"
	"tt-cloud-enterprise/tools/kafka"

	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/utils/constackafka"
)

type sEventCenter struct {
	producer *constackafka.ProducerClient
}

func init() {
	service.RegisterEventCenter(newSEventCenter())
}

func newSEventCenter() *sEventCenter {
	return &sEventCenter{}
}

func (this *sEventCenter) Start() {
	sync.Once{}.Do(func() {
		this.producer = kafka.NewEventCenterProducer(context.Background())
	})
}

func (this *sEventCenter) ExtractEvent(user *dto.ContextUser, path, mothod string, body []byte) {

	fmt.Printf(" extract event, user: %+v, path: %s, mothod: %s, body: %s\n", user, path, mothod, body)

}
