package resource

import (
	"github.com/gogf/gf/v2/frame/g"
	k8s2 "tt-cloud-enterprise/internal/model/dto/k8s"
)

type ListResourceWorkloadRelatedPodReq struct {
	g.Meta    `path:"/resource/workload/related/pod-name/list" tags:"deployments" method:"get" summary:"get resource workload related pod name"`
	ClusterId int    `json:"clusterId" v:"required#Require cluster."`
	Namespace string `json:"namespace" v:"required#Require namespace."`
	Name      string `json:"name" v:"required#Require name."`
	Kind      string `json:"kind" v:"required#Require kind."`
}

type ListWorkloadLogsReq struct {
	g.Meta       `path:"/resource/workload/logs/list" tags:"pods" method:"get" summary:"pod logs list"`
	ClusterId    int    `json:"clusterId"`
	Namespace    string `json:"namespace"`
	WorkloadName string `json:"workloadName"`
	WorkloadKind string `json:"workloadKind"`
	Container    string `json:"containerName"`
	Tail         int    `json:"tail" d:"100"`
}

type SearchWorkloadLogsReq struct {
	g.Meta       `path:"/resource/workload/logs/search" tags:"pods" method:"post" summary:"pod logs search"`
	ClusterId    int    `json:"clusterId"`
	Namespace    string `json:"namespace"`
	WorkloadName string `json:"workloadName"`
	WorkloadKind string `json:"workloadKind"`
	Container    string `json:"containerName"`
	k8s2.PodLogSearchConditionV2
}
