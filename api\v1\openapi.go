package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"tt-cloud-enterprise/internal/consts/praconsts"
	"tt-cloud-enterprise/internal/model/dto"
	"tt-cloud-enterprise/internal/model/dto/k8s"
	multiresourceDto "tt-cloud-enterprise/internal/model/dto/multiresource"
	"tt-cloud-enterprise/internal/model/dto/notify"
	"tt-cloud-enterprise/internal/model/dto/subnamespace"
	k8sTools "tt-cloud-enterprise/tools/k8s"
)

type OpenApiDocListReq struct {
	g.Meta `path:"/doc/list" tags:"OpenApi" method:"get" summary:"系统文档列表地址"`
}

type OpenClusterListReq struct {
	g.Meta  `path:"/resource/cluster/list" tags:"OpenApi" method:"get" summary:"cluster list"`
	IsReady int `json:"is_ready" d:"1" dc:"是否已经就绪"`
}

type OpenNamespaceListReq struct {
	g.Meta    `path:"/resource/namespace/list" tags:"OpenApi" method:"get" summary:"namespace list"`
	ClusterID int `json:"cluster_id" v:"required# require cluster id"`
}

type OpenPodListReq struct {
	g.Meta    `path:"/resource/pod/list" tags:"OpenApi" method:"get" summary:"pod list"`
	ClusterID int    `json:"cluster_id" v:"required# require cluster id"`
	Namespace string `json:"namespace" v:"required# require namespace"`
}

type OpenContainerListReq struct {
	g.Meta    `path:"/resource/container/list" tags:"OpenApi" method:"get" summary:"pod related container list."`
	ClusterID int    `json:"cluster_id" v:"required# require cluster id"`
	Namespace string `json:"namespace" v:"required# require namespace"`
	Pod       string `json:"pod" v:"required# require pod"`
}

type OpenClusterResourceMetricsListReq struct {
	g.Meta `path:"/resource/metrics/cluster/list" tags:"OpenApi" method:"get" summary:"cluster cpu mem list"`
}

type OpenClusterResourceMetricsAmountReq struct {
	g.Meta `path:"/resource/metrics/cluster/amount" tags:"OpenApi" method:"get" summary:"cluster cpu mem amount"`
}

type OpenNodeResourceMetricsListReq struct {
	g.Meta    `path:"/resource/metrics/node/list" tags:"OpenApi" method:"get" summary:"node cpu mem statistics"`
	ClusterID int `json:"cluster_id" v:"required# require cluster id"`
}

//----------------------灵犀平台接口-------------------------

type OpenApiPermissionNamespaceListReq struct {
	g.Meta `path:"/permission/namespace/list" tags:"OpenApi" method:"post" summary:"auth namespace list"`
}

type OpenApiPermissionClusterAdminReq struct {
	g.Meta           `path:"/permission/cluster-admin" tags:"OpenApi" method:"post" summary:"auth cluster admin"`
	Email            string   `json:"email" v:"required# require email "`
	Namespaces       []string `json:"namespaces"`
	AuthClusterAdmin bool     `json:"auth_cluster_admin"`
}

/**
灵犀平台新接口:
	1. 用于授权namespace下所有资源的(r|w|x)权限 ------>
	2. 用于授权某个cluster某个namespace下多种资源(deploy,pod,svc,vs)的(r|w|x)权限
*/

type OpenApiPermissionNamespaceReq struct {
	g.Meta     `path:"/permission/namespace" tags:"OpenApi" method:"post" summary:"auth namespace"`
	Email      string                `json:"email" v:"required# require email "`
	Cluster    string                `json:"cluster"`
	Namespaces []string              `json:"namespaces"`
	Authority  []praconsts.Authority `json:"authority"`
}

// OpenApiUnPermissionNamespaceReq 用于取消授权某个cluster某个namespace下多种资源的(r|w|x)权限
type OpenApiUnPermissionNamespaceReq struct {
	g.Meta     `path:"/unpermission/namespace" tags:"OpenApi" method:"post" summary:"auth namespace"`
	UserNames  []string              `json:"userNames"`
	Clusters   []string              `json:"clusters"`
	Namespaces []string              `json:"namespaces"`
	Authority  []praconsts.Authority `json:"authority"`
}

// OpenApiResourcePermissionReq 用于授权某个cluster某个namespace下多种资源(deploy,pod,svc,vs)的(r|w|x)权限
type OpenApiResourcePermissionReq struct {
	g.Meta    `path:"/permission/resource" tags:"OpenApi" method:"post" summary:"permission k8s resource"`
	Email     string                `json:"email" v:"required# require email "`
	Cluster   string                `json:"cluster"`
	Namespace string                `json:"namespace"`
	Kind      praconsts.K8sRsKind   `json:"kind"`
	Resource  []string              `json:"resource"`
	Authority []praconsts.Authority `json:"authority"`
}

type OpenApiListDeployPermissionReq struct {
	g.Meta    `path:"/permission/deploy/list" tags:"OpenApi" method:"post" summary:"permission k8s deploy list"`
	Cluster   string `json:"cluster"`
	Namespace string `json:"namespace"`
}

type OpenApiListResourceKindReq struct {
	g.Meta `path:"/permission/kind/list" tags:"OpenApi" method:"post" summary:"permission k8s kind list"`
}

type OpenApiListResourceAuthorityReq struct {
	g.Meta `path:"/permission/authority/list" tags:"OpenApi" method:"post" summary:"permission k8s authority list"`
}

type OpenApiListClusterNameReq struct {
	g.Meta `path:"/permission/cluster/list" tags:"OpenApi" method:"post" summary:"permission k8s clustername list"`
}

type OpenApiListNamespaceByClusterReq struct {
	g.Meta  `path:"/permission/namespace/cluster/list" tags:"OpenApi" method:"post" summary:"permission k8s namespace list"`
	Cluster string `json:"cluster"`
}

//---kubectl---

// OpenApiKubectlListClusterRoleReq 返回支持的clusterrole权限
type OpenApiKubectlListClusterRoleReq struct {
	g.Meta `path:"/permission/kubectl/role/list" tags:"OpenApi" method:"get" summary:"kubectl clusterrole list"`
}

// OpenApiKubectlPermissionReq 灵犀平台授予kubectl权限
type OpenApiKubectlPermissionReq struct {
	g.Meta    `path:"/permission/kubectl/auth-clusterrole" tags:"OpenApi" method:"post" summary:"permission kubectl auth"`
	Email     string `json:"email" v:"required# require email "`
	Cluster   string `json:"cluster"`
	Namespace string `json:"namespace"`
	Kind      string `json:"kind"`
}

// OpenApiKubeconfigProxyWithSystemReq 系统授权为用户在指定集群授权多个namespace的kubeconfig权限
type OpenApiKubeconfigProxyWithSystemReq struct {
	g.Meta    `path:"/permission/kubeconfig/system/auth-clusterrole" tags:"OpenApi" method:"post" summary:"permission kubeconfig auth with system"`
	UserId    int      `json:"user_id" v:"required# require email "`
	Cluster   string   `json:"cluster"`
	Namespace []string `json:"namespace"`
	Kind      string   `json:"kind"`
}

// OpenApiKubeconfigProxyWithWorkSheetReq 用于灵犀工单 为用户在指定集群授权namespace的kubeconfig权限
type OpenApiKubeconfigProxyWithWorkSheetReq struct {
	g.Meta    `path:"/permission/kubeconfig/work-sheet/auth-clusterrole" tags:"OpenApi" method:"post" summary:"permission kubeconfig auth with bpm"`
	Email     string   `json:"email" v:"required# require email "`
	Cluster   string   `json:"cluster"`
	Namespace []string `json:"namespace"`
	Kind      string   `json:"kind"`
}

type OpenApiAuthTokenListReq struct {
	g.Meta `path:"/auth-token/list" tags:"OpenApi" method:"get" summary:"list openapi token"`
	ListReq
}

type OpenApiAuthCreateTokenReq struct {
	g.Meta `path:"/auth-token/create" tags:"OpenApi" method:"post" summary:"create openapi token"`
	Name   string `json:"name" v:"required# Require name"`
	Desc   string `json:"desc" v:"required# Require desc"`
}

type OpenApiAuthDeleteTokenReq struct {
	g.Meta `path:"/auth-token/delete/:pid" tags:"OpenApi" method:"delete" summary:"delete openapi token"`
}

type OpenApiAuthCheckReq struct {
	g.Meta `path:"/auth-token/check" tags:"OpenApi" method:"post" summary:"check openapi token"`
	Token  string `json:"token" v:"required# Require token"`
}

//-----------------------cicd---------
//-----------------namespace

// OpenApiK8sNamespaceCopyReq 拷贝命名空间 , 同时再copy originnamespace下所有的rolebinding
type OpenApiK8sNamespaceCopyReq struct {
	g.Meta          `path:"/k8s/namespace/copy" tags:"OpenApi" method:"post" summary:"copy k8s namespace"`
	ClusterName     string            `json:"cluster_name,omitempty" `
	ClusterCloudId  string            `json:"cluster_cloud_id,omitempty"`
	OriginNamespace string            `json:"origin_namespace" v:"required# Require origin_namespace"`
	TargetNamespace string            `json:"target_namespace" v:"required# Require target_namespace"`
	Labels          map[string]string `json:"labels"`
}

// OpenApiK8sNamespaceDeleteReq 删除指定集群指定namespace和它所有的资源及namespace
type OpenApiK8sNamespaceDeleteReq struct {
	g.Meta         `path:"/k8s/namespace/delete" tags:"OpenApi" method:"delete" summary:"delete k8s namespace"`
	ClusterName    string `json:"cluster_name,omitempty" `
	ClusterCloudId string `json:"cluster_cloud_id,omitempty"`
	Namespace      string `json:"namespace" v:"required# Require namespace"`
}

// OpenApiK8sNamespaceCreateReq 创建指定集群的namespace
type OpenApiK8sNamespaceCreateReq struct {
	g.Meta         `path:"/k8s/namespace/create" tags:"OpenApi" method:"post" summary:"create k8s namespace"`
	ClusterName    string            `json:"cluster_name,omitempty" `
	ClusterCloudId string            `json:"cluster_cloud_id,omitempty"`
	NamespaceName  string            `json:"namespace_name"`
	Labels         map[string]string `json:"labels"`
}

// OpenApiK8sNamespaceGetReq 获取指定集群的namespace详情
type OpenApiK8sNamespaceGetReq struct {
	g.Meta         `path:"/k8s/namespace/get" tags:"OpenApi" method:"post" summary:"get k8s namespace detail"`
	ClusterName    string `json:"cluster_name,omitempty" `
	ClusterCloudId string `json:"cluster_cloud_id,omitempty"`
	NamespaceName  string `json:"namespace_name"`
}

// GetOpenApiK8sNamespaceGetReq 获取指定集群的namespace详情 get版
type GetOpenApiK8sNamespaceGetReq struct {
	g.Meta         `path:"/k8s/namespace/get" tags:"OpenApi" method:"get" summary:"get k8s namespace detail"`
	ClusterName    string `json:"cluster_name,omitempty" `
	ClusterCloudId string `json:"cluster_cloud_id,omitempty"`
	NamespaceName  string `json:"namespace_name"`
}

// OpenApiK8sNamespaceListReq 获取指定集群的namespace列表
type OpenApiK8sNamespaceListReq struct {
	g.Meta         `path:"/k8s/namespace/list" tags:"OpenApi" method:"post" summary:"list k8s namespace"`
	ClusterName    string `json:"cluster_name,omitempty" `
	ClusterCloudId string `json:"cluster_cloud_id,omitempty"`
}

// GetOpenApiK8sNamespaceListReq 获取指定集群的namespace列表 get版
type GetOpenApiK8sNamespaceListReq struct {
	g.Meta         `path:"/k8s/namespace/list" tags:"OpenApi" method:"get" summary:"list k8s namespace"`
	ClusterName    string `json:"cluster_name,omitempty" `
	ClusterCloudId string `json:"cluster_cloud_id,omitempty"`
}

// OpenApiK8sAuthCopyReq 复制源namespace下的用户平台权限到目标namespace
type OpenApiK8sAuthCopyReq struct {
	g.Meta          `path:"/k8s/auth/copy" tags:"OpenApi" method:"post" summary:"copy k8s auth"`
	ClusterName     string   `json:"cluster_name" `
	ClusterCloudId  string   `json:"cluster_cloud_id"`
	OriginNamespace string   `json:"origin_namespace" v:"required# Require origin_namespace"`
	TargetNamespace string   `json:"target_namespace" v:"required# Require target_namespace"`
	UserName        []string `json:"user_name" v:"required# Require user_name"`
}

// OpenApiK8sResourceCopyReq 复制指定的资源从origin到target
type OpenApiK8sResourceCopyReq struct {
	g.Meta          `path:"/k8s/resource/copy" tags:"OpenApi" method:"post" summary:"copy k8s resource"`
	ClusterName     string `json:"cluster_name" `
	ClusterCloudId  string `json:"cluster_cloud_id"`
	OriginNamespace string `json:"origin_namespace" v:"required# Require origin_namespace"`
	TargetNamespace string `json:"target_namespace" v:"required# Require target_namespace"`
	ResourceKind    string `json:"resource_kind" v:"required# Require resource_kind"`
	ResourceName    string `json:"resource_name" v:"required# Require resource_name"`
}

// OpenApiListK8sResourceKindReq 返回平台支持资源复制的 资源种类
type OpenApiListK8sResourceKindReq struct {
	g.Meta `path:"/k8s/resource/kind/list" tags:"OpenApi" method:"post" summary:"list k8s resource kind"`
}

// GetOpenApiListK8sResourceKindReq 返回平台支持资源复制的 资源种类 get版
type GetOpenApiListK8sResourceKindReq struct {
	g.Meta `path:"/k8s/resource/kind/list" tags:"OpenApi" method:"get" summary:"list k8s resource kind"`
}

type OpenApiK8sRes struct {
	IsSuccess     bool              `json:"is_success"`
	ErrorResource map[string]string `json:"error_resource"`
}

// ------------------------ cicd 新版接口 -----------------------------

// ListOpenApiCicdK8sClusterReq 根据用户信息和环境展示 集群列表  userIds和env为 and关系
type ListOpenApiCicdK8sClusterReq struct {
	g.Meta  `path:"/cicd/cluster/list" tags:"OpenApi" method:"post" summary:"list cluster by cicd"`
	UserIds []string `json:"userId"`
	Env     string   `json:"env"`
}

type ListOpenApiCicdK8sClusterRes struct {
	ClusterList []string `json:"clusterList"`
}

// ListOpenApiCicdK8sClusterNamespaceReq 根据用户信息和ns类型展示 namespace列表  userIds和type为 and关系
type ListOpenApiCicdK8sClusterNamespaceReq struct {
	g.Meta  `path:"/cicd/cluster/namespace/list" tags:"OpenApi" method:"post" summary:"list cluster namespace by cicd"`
	Cluster string   `json:"cluster"  v:"required# Require cluster"`
	UserIds []string `json:"userId"`
	Type    string   `json:"type"`
}

type ListOpenApiCicdK8sClusterNamespaceRes struct {
	NamespaceList []subnamespace.NamespaceElem `json:"namespaceList"`
}

// CheckExistOpenApiCicdK8sClusterNamespaceReq 检查对应集群的某个namespace是否存在
type CheckExistOpenApiCicdK8sClusterNamespaceReq struct {
	g.Meta    `path:"/cicd/cluster/namespace/exist" tags:"OpenApi" method:"get" summary:"check cluster namespace exist by cicd"`
	Cluster   string `json:"cluster"  v:"required# Require cluster"`
	Namespace string `json:"namespace"  v:"required# Require namespace"`
}

type CheckExistOpenApiCicdK8sClusterNamespaceRes struct {
	Exist bool `json:"exist"`
}

// CreateOpenApiCicdK8sClusterSubNamespaceReq 根据基准环境创建子环境
type CreateOpenApiCicdK8sClusterSubNamespaceReq struct {
	g.Meta          `path:"/cicd/cluster/sub-namespace/create" tags:"OpenApi" method:"post" summary:"create cluster sub-namespace by cicd"`
	Cluster         string            `json:"cluster"  v:"required# Require cluster"`
	OriginNamespace string            `json:"originNamespace" v:"required# Require originNamespace"`
	TargetNamespace string            `json:"targetNamespace" v:"required# Require targetNamespace"`
	Labels          map[string]string `json:"labels"`
	UserIds         []string          `json:"userId"`
}

// GetOpenApiCicdK8sClusterSubNamespaceReq 查询子环境和关联的流量标记
type GetOpenApiCicdK8sClusterSubNamespaceReq struct {
	g.Meta    `path:"/cicd/cluster/sub-namespace/get" tags:"OpenApi" method:"get" summary:"get cluster sub-namespace by cicd"`
	Cluster   string `json:"cluster"  v:"required# Require cluster"`
	Namespace string `json:"namespace" v:"required# Require namespace"`
}

type GetOpenApiCicdK8sClusterSubNamespaceRes struct {
	subnamespace.NamespaceElem
}

// ListCopyClusterNamespaceResourceReq 复制资源列表
type ListCopyClusterNamespaceResourceReq struct {
	g.Meta    `path:"/cicd/cluster/namespace/resource-name/list" tags:"OpenApi" method:"get" summary:"list resource by cicd"`
	Cluster   string `json:"cluster"  v:"required# Require cluster"`
	Namespace string `json:"namespace" v:"required# Require namespace"`
	Kind      string `json:"kind" v:"required# Require kind"`
	Search    string `json:"search"`
	Page      int    `json:"page" d:"1"`
	Size      int    `json:"size" d:"10"`
}

// CopyClusterNamespaceResourceReq 复制k8s资源
type CopyClusterNamespaceResourceReq struct {
	g.Meta          `path:"/cicd/cluster/namespace/resource/copy" tags:"OpenApi" method:"post" summary:"copy resource by cicd"`
	Cluster         string                  `json:"cluster"  v:"required# Require cluster"`
	OriginNamespace string                  `json:"originNamespace" v:"required# Require originNamespace"`
	TargetNamespace string                  `json:"targetNamespace" v:"required# Require targetNamespace"`
	Resources       []subnamespace.Resource `json:"resources"`
}

// GetReadyReplicasWorkloadReq 查询workload 可用副本数
type GetReadyReplicasWorkloadReq struct {
	g.Meta       `path:"/cicd/cluster/namespace/workload/ready-replicas/get" tags:"OpenApi" method:"get" summary:"get workload ready-replicas by cicd"`
	Cluster      string `json:"cluster"  v:"required# Require cluster"`
	Namespace    string `json:"namespace" v:"required# Require namespace"`
	Kind         string `json:"kind" v:"required# Require kind" `
	ResourceName string `json:"resourceName" v:"required# Require resourceName" `
}

type GetReadyReplicasWorkloadRes struct {
	k8s.WorkloadReadyReplicas
}

// AddSubEnvRouteReq 新增路由 , 用于在子环境部署服务后 在基准环境的vs(delegate)配置一条路由
type AddSubEnvRouteReq struct {
	g.Meta       `path:"/cicd/cluster/namespace/route/add" tags:"OpenApi" method:"post" summary:"add sub env route by cicd"`
	Cluster      string `json:"cluster"  v:"required# Require cluster"`
	SubNamespace string `json:"subNamespace" v:"required# Require subNamespace"`
	ServiceName  string `json:"serviceName" v:"required# Require serviceName" `
}

// DeleteSubEnvRouteReq 删除路由 , 用于在子环境下线服务后 在基准环境的vs(delegate)修改一条路由
type DeleteSubEnvRouteReq struct {
	g.Meta       `path:"/cicd/cluster/namespace/route/delete" tags:"OpenApi" method:"delete" summary:"delete sub env route by cicd"`
	Cluster      string `json:"cluster"  v:"required# Require cluster"`
	SubNamespace string `json:"subNamespace" v:"required# Require subNamespace"`
	ServiceName  string `json:"serviceName" v:"required# Require serviceName" `
}

// AddSubEnvTrafficMarkReq 新增流量标记 , 用于在子环境创建好之后 , 新增一个标记
type AddSubEnvTrafficMarkReq struct {
	g.Meta       `path:"/cicd/cluster/namespace/traffic-mark/add" tags:"OpenApi" method:"post" summary:"add sub env traffic mark by cicd"`
	Cluster      string `json:"cluster"  v:"required# Require cluster"`
	SubNamespace string `json:"subNamespace" v:"required# Require subNamespace"`
	TrafficMark  string `json:"trafficMark" v:"required# Require TrafficMark" `
}

// UpdateSubEnvTrafficMarkReq 修改流量标记 , 修改标记后  要触发修改vs-delegate的配置
type UpdateSubEnvTrafficMarkReq struct {
	g.Meta       `path:"/cicd/cluster/namespace/traffic-mark/update" tags:"OpenApi" method:"post" summary:"update sub env traffic mark by cicd"`
	Cluster      string `json:"cluster"  v:"required# Require cluster"`
	SubNamespace string `json:"subNamespace" v:"required# Require subNamespace"`
	TrafficMark  string `json:"trafficMark" v:"required# Require trafficMark" `
}

// DeleteSubEnvTrafficMarkReq 删除流量标记 , 删除所有和这个标记关联的vs路由
type DeleteSubEnvTrafficMarkReq struct {
	g.Meta       `path:"/cicd/cluster/namespace/traffic-mark/delete" tags:"OpenApi" method:"delete" summary:"delete sub env traffic mark by cicd"`
	Cluster      string `json:"cluster"  v:"required# Require cluster"`
	SubNamespace string `json:"subNamespace" v:"required# Require subNamespace"`
}

//-----------------------Istio---------

// OpenApiListVirtualServiceReq 返回指定集群下的指定namespace下的virtual services
type OpenApiListVirtualServiceReq struct {
	g.Meta      `path:"/istio/resource/virtualservice/list" tags:"OpenApi" method:"get" summary:"list virtual service"`
	ClusterName string `json:"cluster_name" v:"required# Require cluster_name"`
	Namespace   string `json:"namespace" v:"required# Require namespace"`
	Search      string `json:"search" dc:"vs name for searching virtual service"`
}

// OpenApiGetVirtualServiceDetailsReq 返回指定virtual service的详情
type OpenApiGetVirtualServiceDetailsReq struct {
	g.Meta       `path:"/istio/resource/virtualservice/get" method:"get" tags:"Virtual Service" sm:"virtual service details"`
	ResourceName string `json:"resource_name" v:"required# Require resource_name"`
	ClusterName  string `json:"cluster_name" v:"required# Require cluster_name"`
	Namespace    string `json:"namespace" v:"required# Require namespace"`
}

// OpenApiDeleteVirtualServiceReq 删除指定的vs
type OpenApiDeleteVirtualServiceReq struct {
	g.Meta       `path:"/istio/resource/virtualservice/delete" method:"delete" tags:"Virtual Service" sm:"delete virtual service"`
	ResourceName string `json:"resource_name" v:"required# Require resource_name"`
	ClusterName  string `json:"cluster_name" v:"required# Require cluster_name"`
	Namespace    string `json:"namespace" v:"required# Require namespace"`
}

// OpenApiCreateVirtualServiceReq 新建vs
type OpenApiCreateVirtualServiceReq struct {
	g.Meta      `path:"/istio/resource/virtualservice/create" method:"post" tags:"Virtual Service"`
	Data        interface{} `json:"data" v:"required#Require test." dc:"text for k8s"`
	ClusterName string      `json:"cluster_name" v:"required# Require cluster_name"`
	Namespace   string      `json:"namespace" v:"required# Require namespace"`
}

// OpenApiUpdateVirtualServiceReq 更新vs
type OpenApiUpdateVirtualServiceReq struct {
	g.Meta      `path:"/istio/resource/virtualservice/update" method:"post" tags:"Virtual Service"`
	Data        interface{} `json:"data" v:"required#Require test." dc:"text for k8s"`
	ClusterName string      `json:"cluster_name" v:"required# Require cluster_name"`
	Namespace   string      `json:"namespace" v:"required# Require namespace"`
}

// OpenApiCreateCopyVirtualServiceReq 将指定的vs复制到目标环境中
type OpenApiCreateCopyVirtualServiceReq struct {
	g.Meta            `path:"/istio/resource/virtualservice/copy/create" method:"post" tags:"Virtual Service"`
	NewClusterName    string `json:"new_cluster_name" v:"required#Require cluster."`
	NewNamespace      string `json:"new_namespace" v:"required#Require namespace."`
	OriginClusterName string `json:"origin_cluster_name" v:"required#Require cluster."`
	OriginNamespace   string `json:"origin_namespace" v:"required#Require namespace."`
	ResourceName      string `json:"resource_name" v:"required#Require resourcename."`
}

// OpenApiListResourceDestinationRuleReq 返回指定集群下的指定namespace下的destinationRule
type OpenApiListResourceDestinationRuleReq struct {
	g.Meta      `path:"/istio/resource/destinationrule/list" method:"get" tags:"destinationrule"`
	ClusterName string `json:"cluster_name" v:"required# Require cluster_name"`
	Namespace   string `json:"namespace" v:"required# Require namespace"`
	Search      string `json:"search" dc:"dr name for searching destinationRule"`
}

// OpenApiGetDestinationRuleDetailsReq 返回指定的destinationRule详情
type OpenApiGetDestinationRuleDetailsReq struct {
	g.Meta       `path:"/istio/resource/destinationrule/get" method:"get" tags:"destinationrule"`
	ResourceName string `json:"resource_name" v:"required#Require resourcename." dc:"dr name"`
	ClusterName  string `json:"cluster_name" v:"required# Require cluster_name"`
	Namespace    string `json:"namespace" v:"required# Require namespace"`
}

// OpenApiCreateCopyDestinationRuleReq 将指定的dr复制到目标环境中
type OpenApiCreateCopyDestinationRuleReq struct {
	g.Meta            `path:"/istio/resource/destinationrule/copy/create" method:"post" tags:"destinationrule"`
	NewClusterName    string `json:"new_cluster_name" v:"required#Require cluster."`
	NewNamespace      string `json:"new_namespace" v:"required#Require namespace."`
	OriginClusterName string `json:"origin_cluster_name" v:"required#Require cluster."`
	OriginNamespace   string `json:"origin_namespace" v:"required#Require namespace."`
	ResourceName      string `json:"resource_name" v:"required#Require resourcename."`
}

// OpenApiCreateDestinationRuleReq 新建dr
type OpenApiCreateDestinationRuleReq struct {
	g.Meta      `path:"/istio/resource/destinationrule/create" method:"post" tags:"destinationrule"`
	Data        interface{} `json:"data" v:"required#Require test." dc:"text for k8s"`
	ClusterName string      `json:"cluster_name" v:"required# Require cluster_name"`
	Namespace   string      `json:"namespace" v:"required# Require namespace"`
}

// OpenApiUpdateDestinationRuleReq 更新dr
type OpenApiUpdateDestinationRuleReq struct {
	g.Meta      `path:"/istio/resource/destinationrule/update" method:"post" tags:"destinationrule"`
	Data        interface{} `json:"data" v:"required#Require test." dc:"text for k8s"`
	ClusterName string      `json:"cluster_name" v:"required# Require cluster_name"`
	Namespace   string      `json:"namespace" v:"required# Require namespace"`
}

// OpenApiDeleteDestinationRuleReq 删除dr
type OpenApiDeleteDestinationRuleReq struct {
	g.Meta       `path:"/istio/resource/destinationrule/delete" method:"delete" tags:"destinationrule"`
	ResourceName string `json:"resource_name" v:"required#Require resourcename." dc:"dr name"`
	ClusterName  string `json:"cluster_name" v:"required# Require cluster_name"`
	Namespace    string `json:"namespace" v:"required# Require namespace"`
}

//any k8s resource

// OpenApiK8sResourceAnyGetReq 获取指定gvr资源的详情
type OpenApiK8sResourceAnyGetReq struct {
	g.Meta         `path:"/k8s/resource/any/get" tags:"OpenApi" method:"post" summary:"get k8s resource"`
	ClusterName    string   `json:"cluster_name"`
	ClusterCloudId string   `json:"cluster_cloud_id"`
	Namespace      string   `json:"namespace"`
	Group          string   `json:"group"`
	Version        string   `json:"version" v:"required# Require version"`
	Resource       string   `json:"resource" v:"required# Require resource"`
	ResourceNames  []string `json:"resource_names"`
}

// GetOpenApiK8sResourceAnyGetReq 获取指定gvr资源的详情 get版
type GetOpenApiK8sResourceAnyGetReq struct {
	g.Meta         `path:"/k8s/resource/any/get" tags:"OpenApi" method:"get" summary:"get k8s resource"`
	ClusterName    string   `json:"cluster_name"`
	ClusterCloudId string   `json:"cluster_cloud_id"`
	Namespace      string   `json:"namespace"`
	Group          string   `json:"group"`
	Version        string   `json:"version" v:"required# Require version"`
	Resource       string   `json:"resource" v:"required# Require resource"`
	ResourceNames  []string `json:"resource_names"`
}

// OpenApiK8sResourceAnyListReq 查看指定gvr资源列表
type OpenApiK8sResourceAnyListReq struct {
	g.Meta         `path:"/k8s/resource/any/list" tags:"OpenApi" method:"post" summary:"list k8s resource"`
	ClusterName    string                   `json:"cluster_name"`
	ClusterCloudId string                   `json:"cluster_cloud_id"`
	Namespace      string                   `json:"namespace"`
	Group          string                   `json:"group"`
	Version        string                   `json:"version" v:"required# Require version"`
	Resource       string                   `json:"resource" v:"required# Require resource"`
	LabelSelector  []k8sTools.LabelSelector `json:"label_selector"`
	FiledSelector  []k8sTools.LabelSelector `json:"filedSelector"`
}

// GetOpenApiK8sResourceAnyListReq 查看指定gvr资源列表  get版
type GetOpenApiK8sResourceAnyListReq struct {
	g.Meta         `path:"/k8s/resource/any/list" tags:"OpenApi" method:"get" summary:"list k8s resource"`
	ClusterName    string                   `json:"cluster_name"`
	ClusterCloudId string                   `json:"cluster_cloud_id"`
	Namespace      string                   `json:"namespace"`
	Group          string                   `json:"group"`
	Version        string                   `json:"version" v:"required# Require version"`
	Resource       string                   `json:"resource" v:"required# Require resource"`
	LabelSelector  []k8sTools.LabelSelector `json:"labelSelector"`
}

// OpenApiK8sResourceAnyCreateReq 创建指定gvr的k8s资源
type OpenApiK8sResourceAnyCreateReq struct {
	g.Meta         `path:"/k8s/resource/any/create" tags:"OpenApi" method:"post" summary:"create k8s resource"`
	ClusterName    string `json:"cluster_name"`
	ClusterCloudId string `json:"cluster_cloud_id"`
	Yaml           string `json:"yaml" v:"required# Require cluster_name"`
}

// OpenApiK8sResourceAnyUpdateReq 更新指定gvr的k8s资源
type OpenApiK8sResourceAnyUpdateReq struct {
	g.Meta         `path:"/k8s/resource/any/update" tags:"OpenApi" method:"post" summary:"update k8s resource"`
	ClusterName    string `json:"cluster_name"`
	ClusterCloudId string `json:"cluster_cloud_id"`
	Yaml           string `json:"yaml" v:"required# Require cluster_name"`
}

// OpenApiK8sResourceAnyDeleteReq 删除指定gvr的k8s资源
type OpenApiK8sResourceAnyDeleteReq struct {
	g.Meta         `path:"/k8s/resource/any/delete" tags:"OpenApi" method:"delete" summary:"delete k8s resource"`
	ClusterName    string   `json:"cluster_name"`
	ClusterCloudId string   `json:"cluster_cloud_id"`
	Namespace      string   `json:"namespace"`
	Group          string   `json:"group"`
	Version        string   `json:"version" v:"required# Require version"`
	Resource       string   `json:"resource" v:"required# Require resource"`
	ResourceNames  []string `json:"resource_names"`
}

//----------------label--------------

// OpenApiK8sResourceLabelAddReq 对k8s资源添加labels
type OpenApiK8sResourceLabelAddReq struct {
	g.Meta         `path:"/k8s/resource/labels/add" tags:"OpenApi" method:"post" summary:"add k8s resource labels"`
	ClusterName    string            `json:"cluster_name"`
	ClusterCloudId string            `json:"cluster_cloud_id"`
	Namespace      string            `json:"namespace"`
	Group          string            `json:"group"`
	Version        string            `json:"version" v:"required# Require version"`
	Kind           string            `json:"kind" v:"required# Require kind"`
	ResourceNames  []string          `json:"resource_names"`
	Labels         map[string]string `json:"labels"`
}

// OpenApiK8sResourceLabelDeleteReq 删除指定k8s资源的labels
type OpenApiK8sResourceLabelDeleteReq struct {
	g.Meta         `path:"/k8s/resource/labels/delete" tags:"OpenApi" method:"delete" summary:"delete k8s resource labels"`
	ClusterName    string   `json:"cluster_name"`
	ClusterCloudId string   `json:"cluster_cloud_id"`
	Namespace      string   `json:"namespace"`
	Group          string   `json:"group"`
	Version        string   `json:"version" v:"required# Require version"`
	Kind           string   `json:"kind" v:"required# Require kind"`
	ResourceNames  []string `json:"resource_names"`
	Labels         []string `json:"labels"`
}

// OpenApiK8sResourceLabelUpdateReq 更新指定k8s资源的labels
type OpenApiK8sResourceLabelUpdateReq struct {
	g.Meta         `path:"/k8s/resource/labels/update" tags:"OpenApi" method:"post" summary:"update k8s resource labels"`
	ClusterName    string            `json:"cluster_name"`
	ClusterCloudId string            `json:"cluster_cloud_id"`
	Namespace      string            `json:"namespace"`
	Group          string            `json:"group"`
	Version        string            `json:"version" v:"required# Require version"`
	Kind           string            `json:"kind" v:"required# Require kind"`
	ResourceName   string            `json:"resource_name"`
	Labels         map[string]string `json:"labels"`
}

// OpenApiK8sResourceLabelGetReq 查看指定k8s资源的labels
type OpenApiK8sResourceLabelGetReq struct {
	g.Meta         `path:"/k8s/resource/labels/get" tags:"OpenApi" method:"post" summary:"get k8s resource labels detail"`
	ClusterName    string `json:"cluster_name"`
	ClusterCloudId string `json:"cluster_cloud_id"`
	Namespace      string `json:"namespace"`
	Group          string `json:"group"`
	Version        string `json:"version" v:"required# Require version"`
	Kind           string `json:"kind" v:"required# Require kind"`
	Resource       string `json:"resource_name"`
}

// GetOpenApiK8sResourceLabelGetReq 查看指定k8s资源的labels get版
type GetOpenApiK8sResourceLabelGetReq struct {
	g.Meta         `path:"/k8s/resource/labels/get" tags:"OpenApi" method:"get" summary:"get k8s resource labels detail"`
	ClusterName    string `json:"cluster_name"`
	ClusterCloudId string `json:"cluster_cloud_id"`
	Namespace      string `json:"namespace"`
	Group          string `json:"group"`
	Version        string `json:"version" v:"required# Require version"`
	Kind           string `json:"kind" v:"required# Require kind"`
	Resource       string `json:"resource_name"`
}

type OpenApiPodAnalyzeReq struct {
	g.Meta         `path:"/k8s/resource/pod/analyze" tags:"OpenApi" method:"post" summary:"pod analyze"`
	ClusterName    string `json:"clusterName"`
	ClusterCloudId string `json:"clusterCloudId"`
	Namespace      string `json:"namespace"`
	Pod            string `json:"pod"`
}

type OpenApiWorkloadAnalyzeReq struct {
	g.Meta         `path:"/k8s/resource/workload/analyze" tags:"OpenApi" method:"post" summary:"workload analyze"`
	ClusterName    string `json:"clusterName"`
	ClusterCloudId string `json:"clusterCloudId"`
	Namespace      string `json:"namespace"`
	Name           string `json:"name"`
	Kind           string `json:"kind"`
}

// OpenApiK8sResourceAnyBackupReq 备份任意资源接口
type OpenApiK8sResourceAnyBackupReq struct {
	g.Meta         `path:"/k8s/resource/any/backup" tags:"OpenApi" method:"post" summary:"backup k8s resource"`
	ClusterName    string   `json:"clusterName"`
	ClusterCloudId string   `json:"clusterCloudId"`
	Namespace      string   `json:"namespace"`
	Names          []string `json:"names"`
	Group          string   `json:"group"`
	Version        string   `json:"version" v:"required# Require version"`
	Resource       string   `json:"resource" v:"required# Require resource"`
}

type OpenApiK8sResourceHighLevelEventReq struct {
	g.Meta      `path:"/k8s/resource/event/high-level/list" tags:"OpenApi" method:"get" summary:"k8s resource event"`
	ClusterName string `json:"clusterName" v:"required# Require cluster name"`
	Namespace   string `json:"namespace" v:"required# Require namespace"`
	Group       string `json:"group"`
	Version     string `json:"version" v:"required# Require version"`
	Resource    string `json:"resource" v:"required# Require resource"`
	ObjectName  string `json:"objectName" v:"required# Require object name"`
	StartTime   int64  `json:"startTime" v:"required# Require start time"`
	EndTime     int64  `json:"endTime" v:"required# Require end time"`
}

type OpenApiK8sResourceLowLevelEventReq struct {
	g.Meta      `path:"/k8s/resource/event/low-level/list" tags:"OpenApi" method:"get" summary:"k8s resource event"`
	ClusterName string `json:"clusterName" v:"required# Require cluster name"`
	UID         string `json:"uid" v:"required# Require uid"`
	StartTime   int64  `json:"startTime" v:"required# Require start time"`
	EndTime     int64  `json:"endTime" v:"required# Require end time"`
}

// OpenApiGetLbRouteReq 获取当前lb关联的路由信息
type OpenApiGetLbRouteReq struct {
	g.Meta `path:"/k8s/route/related/lb/get" tags:"OpenApi" method:"get" summary:"get lb route"`
	Lb     string `json:"lb" v:"required# Require lb"`
}

// OpenApiGetWorkloadRelatedPodStatusReq 获取workload关联的pod状态
type OpenApiGetWorkloadRelatedPodStatusReq struct {
	g.Meta         `path:"/k8s/resource/workload/status/get" tags:"OpenApi" method:"get" summary:"get workload related pod status"`
	ClusterName    string `json:"clusterName"`
	ClusterCloudId string `json:"clusterCloudId"`
	Namespace      string `json:"namespace" v:"required# Require namespace"`
	Name           string `json:"name" v:"required# Require workload name"`
	Kind           string `json:"kind" v:"required# Require workload kind"`
}

// OpenApiGetPodWithoutClusterStatusReq 获取pod状态(不传集群)
type OpenApiGetPodWithoutClusterStatusReq struct {
	g.Meta    `path:"/k8s/resource/pod/clusterless/status/get" tags:"OpenApi" method:"get" summary:"get pod status without cluster"`
	Namespace string `json:"namespace" v:"required# Require pod namespace"`
	Name      string `json:"name" v:"required# Require pod name"`
}

// OpenApiPageJumpWorkloadDetailReq 跳转workload详情页接口接口
type OpenApiPageJumpWorkloadDetailReq struct {
	g.Meta         `path:"/k8s/resource/workload-detail/page-jump" tags:"OpenApi" method:"get" summary:"get workload related pod status"`
	ClusterName    string `json:"clusterName"`
	ClusterCloudId string `json:"clusterCloudId"`
	Namespace      string `json:"namespace" v:"required# Require namespace"`
	Name           string `json:"name" v:"required# Require workload name"`
	UserName       string `json:"username" v:"required# Require username"`
	Kind           string `json:"kind" v:"required# Require workload kind"`
}

// OpenApiPageJumpPodDetailReq 跳转pod详情页接口
type OpenApiPageJumpPodDetailReq struct {
	g.Meta    `path:"/k8s/resource/pod-clusterless/detail/page-jump" tags:"OpenApi" method:"get" `
	Namespace string `json:"namespace" v:"required# Require namespace"`
	Name      string `json:"name" v:"required# Require workload name"`
	UserName  string `json:"username" v:"required# Require username"`
}

// OpenApiPageJumpPodListReq 跳转pod列表页接口
type OpenApiPageJumpPodListReq struct {
	g.Meta      `path:"/k8s/resource/pod/list/page-jump" tags:"OpenApi" method:"get" `
	ClusterName string `json:"clusterName" v:"required# Require cluster name"`
	Namespace   string `json:"namespace" v:"required# Require namespace"`
	Name        string `json:"name" v:"required# Require pod name"`
	UserName    string `json:"username" v:"required# Require username"`
}

// OpenApiListNodeWithoutClusterReq 查询所有集群的node列表
type OpenApiListNodeWithoutClusterReq struct {
	g.Meta `path:"/k8s/resource/node/clusterless/list" tags:"OpenApi" method:"get" summary:"list node without cluster"`
}

// OpenApiRestartWorkloadReq 重启workload
type OpenApiRestartWorkloadReq struct {
	g.Meta         `path:"/k8s/resource/workload/restart" tags:"OpenApi" method:"post" summary:"restart workload"`
	ClusterName    string `json:"clusterName"`
	ClusterCloudId string `json:"clusterCloudId"`
	Namespace      string `json:"namespace" v:"required# Require namespace"`
	Name           string `json:"name" v:"required# Require workload name"`
	Kind           string `json:"kind" v:"required# Require workload kind"`
}

// OpenApiGetPodDetailReq 查询pod所在的集群命名空间
type OpenApiGetPodDetailReq struct {
	g.Meta `path:"/k8s/resource/pod/get" tags:"OpenApi" method:"get" summary:"get pod detail"`
	PodIp  string `json:"podIp"`
}

type OpenApiMultiResourceSearchReq struct {
	g.Meta `path:"/k8s/resource/multi-resource-search" tags:"OpenApi" method:"post" summary:"multi resource search"`
	multiresourceDto.QueryMultiResourceDto
}

// ------------ 用户相关接口 ---------------------------------------------------------------------
type OpenApiGetUserTokenByAppTokenReq struct {
	g.Meta   `path:"/user/token/get" tags:"OpenApi" method:"get" summary:"get user token by app token"`
	Username string `json:"username" v:"required# Require username"`
}

type OpenApiGetUserTokenByAppTokenRes struct {
	Token string `json:"token"`
}

// --------------------------------------------鉴权接口---------------------------------------------

// OpenApiCheckUserK8sPermissionReq 检查当前用户是否有操作k8s资源的权限
type OpenApiCheckUserK8sPermissionReq struct {
	g.Meta    `path:"/user/k8s/permission/check" tags:"OpenApi" method:"post" summary:"check user permission"`
	Email     string              `json:"email" v:"required# Require email"`
	Cluster   string              `json:"cluster" v:"required# Require email"`
	Namespace string              `json:"namespace" v:"required# Require email"`
	Kind      praconsts.K8sRsKind `json:"kind" v:"required# Require kind"`
}

type OpenApiCheckUserK8sPermissionRes struct {
	IsAllow bool `json:"isAllow"`
}

type OpenApiSendInternalMessageCardReq struct {
	g.Meta      `path:"/feishu/card/internal-message/send" tags:"Feishu" method:"post" summary:"send internal message card"`
	Emails      []string           `json:"emails"`
	Title       string             `json:"title" v:"required"`
	Text        string             `json:"text" v:"required"`
	MessageType notify.MessageType `json:"messageType"`
}

// ----------------------- oprecord -----------------------
type OepnApiOpRecordLogUploadReq struct {
	g.Meta `path:"/op-record/log/upload" tags:"OpenApi" method:"post" summary:"upload operation record log"`
	Data   *[]dto.OpRecord `json:"data" v:"required#Require data."`
}

// ---------------------system---------------------------
type OpenApiPrivateSystemScriptClearJetDevRouteReq struct {
	g.Meta    `path:"/system/private/script/clear-jet-dev-route" tags:"OpenApi" method:"post" summary:"clear jet dev route"`
	Cluster   string `json:"cluster"`
	Namespace string `json:"namespace"`
}
